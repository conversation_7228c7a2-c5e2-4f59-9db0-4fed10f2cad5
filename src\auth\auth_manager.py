#!/usr/bin/env python3
"""
نظام إدارة المصادقة والجلسات
Authentication and Session Management System
"""

import hashlib
import secrets
import time
from typing import Dict, Optional
from functools import wraps
from flask import session, request, redirect, url_for, flash

class AuthManager:
    """مدير المصادقة والجلسات"""
    
    def __init__(self, db_manager, logger):
        self.db = db_manager
        self.logger = logger
        self.session_timeout = 3600  # ساعة واحدة
        
        # إنشاء المستخدم الافتراضي إذا لم يكن موجوداً
        self._create_default_admin()
    
    def _create_default_admin(self):
        """إنشاء مستخدم الإدارة الافتراضي"""
        try:
            # التحقق من وجود المستخدم
            admin_user = self.db.get_user_by_national_id("99151413104")
            
            if not admin_user:
                # إنشاء كلمة مرور مشفرة
                password_hash = self.hash_password("admin")
                
                # إنشاء المستخدم
                user_id = self.db.create_user(
                    national_id="99151413104",
                    username="admin",
                    password_hash=password_hash,
                    full_name="مدير النظام",
                    email="<EMAIL>",
                    role="admin"
                )
                
                self.logger.info(f"تم إنشاء مستخدم الإدارة الافتراضي: admin (ID: {user_id})")
            else:
                self.logger.info("مستخدم الإدارة موجود بالفعل")
                
        except Exception as e:
            self.logger.error(f"خطأ في إنشاء مستخدم الإدارة: {e}")
    
    def hash_password(self, password: str) -> str:
        """تشفير كلمة المرور"""
        # إضافة salt عشوائي
        salt = secrets.token_hex(16)
        # تشفير كلمة المرور مع salt
        password_hash = hashlib.pbkdf2_hmac('sha256', 
                                          password.encode('utf-8'), 
                                          salt.encode('utf-8'), 
                                          100000)
        # دمج salt مع hash
        return salt + password_hash.hex()
    
    def verify_password(self, password: str, password_hash: str) -> bool:
        """التحقق من كلمة المرور"""
        try:
            # استخراج salt (أول 32 حرف)
            salt = password_hash[:32]
            # استخراج hash الأصلي
            original_hash = password_hash[32:]
            
            # تشفير كلمة المرور المدخلة مع نفس salt
            new_hash = hashlib.pbkdf2_hmac('sha256',
                                         password.encode('utf-8'),
                                         salt.encode('utf-8'),
                                         100000)
            
            # مقارنة النتائج
            return new_hash.hex() == original_hash
            
        except Exception as e:
            self.logger.error(f"خطأ في التحقق من كلمة المرور: {e}")
            return False
    
    def authenticate_user(self, identifier: str, password: str) -> Optional[Dict]:
        """مصادقة المستخدم"""
        try:
            # محاولة البحث برقم الهوية أولاً
            user = self.db.get_user_by_national_id(identifier)
            
            # إذا لم يوجد، ابحث باسم المستخدم
            if not user:
                user = self.db.get_user_by_username(identifier)
            
            if not user:
                self.logger.warning(f"محاولة دخول فاشلة: المستخدم غير موجود - {identifier}")
                return None
            
            # التحقق من كلمة المرور
            if not self.verify_password(password, user['password_hash']):
                self.logger.warning(f"محاولة دخول فاشلة: كلمة مرور خاطئة - {identifier}")
                return None
            
            # تحديث آخر تسجيل دخول
            self.db.update_last_login(user['id'])
            
            self.logger.info(f"تسجيل دخول ناجح: {user['username']} ({user['national_id']})")
            
            # إرجاع بيانات المستخدم (بدون كلمة المرور)
            user_data = user.copy()
            del user_data['password_hash']
            return user_data
            
        except Exception as e:
            self.logger.error(f"خطأ في مصادقة المستخدم: {e}")
            return None
    
    def create_session(self, user: Dict):
        """إنشاء جلسة للمستخدم"""
        session['user_id'] = user['id']
        session['username'] = user['username']
        session['national_id'] = user['national_id']
        session['role'] = user['role']
        session['full_name'] = user['full_name']
        session['login_time'] = time.time()
        session.permanent = True
        
        self.logger.info(f"تم إنشاء جلسة للمستخدم: {user['username']}")
    
    def destroy_session(self):
        """إنهاء الجلسة"""
        username = session.get('username', 'غير معروف')
        session.clear()
        self.logger.info(f"تم إنهاء جلسة المستخدم: {username}")
    
    def is_logged_in(self) -> bool:
        """التحقق من تسجيل الدخول"""
        if 'user_id' not in session:
            return False
        
        # التحقق من انتهاء صلاحية الجلسة
        login_time = session.get('login_time', 0)
        if time.time() - login_time > self.session_timeout:
            self.destroy_session()
            return False
        
        return True
    
    def is_admin(self) -> bool:
        """التحقق من صلاحيات الإدارة"""
        return self.is_logged_in() and session.get('role') == 'admin'
    
    def get_current_user(self) -> Optional[Dict]:
        """الحصول على بيانات المستخدم الحالي"""
        if not self.is_logged_in():
            return None
        
        return {
            'id': session.get('user_id'),
            'username': session.get('username'),
            'national_id': session.get('national_id'),
            'role': session.get('role'),
            'full_name': session.get('full_name'),
            'login_time': session.get('login_time')
        }

def login_required(f):
    """decorator للصفحات التي تتطلب تسجيل دخول"""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if 'user_id' not in session:
            flash('يجب تسجيل الدخول للوصول إلى هذه الصفحة', 'warning')
            return redirect(url_for('login'))
        return f(*args, **kwargs)
    return decorated_function

def admin_required(f):
    """decorator للصفحات التي تتطلب صلاحيات إدارة"""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if 'user_id' not in session:
            flash('يجب تسجيل الدخول للوصول إلى هذه الصفحة', 'warning')
            return redirect(url_for('login'))
        
        if session.get('role') != 'admin':
            flash('ليس لديك صلاحية للوصول إلى هذه الصفحة', 'danger')
            return redirect(url_for('dashboard'))
        
        return f(*args, **kwargs)
    return decorated_function
