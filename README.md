# مشروع التعرف على الوجوه باستخدام Python

## وصف المشروع
نظام ذكي للتعرف على الوجوه في الوقت الفعلي باستخدام الكاميرا. يمكن للنظام التعرف على الأشخاص المسجلين مسبقاً وعرض أسمائهم مع نسبة الدقة.

## المميزات
- ✅ التعرف على الوجوه في الوقت الفعلي
- ✅ عرض نسبة دقة التعرف
- ✅ دعم صيغ متعددة للصور (JPG, PNG, JPEG)
- ✅ واجهة سهلة الاستخدام
- ✅ معالجة الأخطاء المحسنة

## المتطلبات
- Python 3.7+
- كاميرا ويب
- المكتبات المطلوبة (انظر requirements.txt)

## التثبيت

### 1. تنزيل المكتبات
```bash
pip install -r requirements.txt
```

أو تنزيل المكتبات منفردة:
```bash
pip install opencv-python
pip install numpy
pip install face-recognition
```

### 2. إضافة صور الأشخاص
- ضع صور الأشخاص في مجلد `persons/`
- استخدم صيغ: JPG, PNG, JPEG
- اسم الملف سيكون اسم الشخص (مثال: `ahmed.jpg`)

## طريقة الاستخدام

### تشغيل البرنامج
```bash
python main.py
```

### التحكم
- **q**: إغلاق البرنامج
- النظام يعمل تلقائياً عند تشغيله

## هيكل المشروع
```
Face-recognition-python-project/
├── main.py              # الملف الرئيسي
├── requirements.txt     # المكتبات المطلوبة
├── README.md           # ملف التوثيق
└── persons/            # مجلد صور الأشخاص
    ├── Salar.jpg
    ├── bill.jpg
    ├── elon.jpg
    └── steve.jpg
```

## كيفية عمل النظام
1. **تحميل الصور**: يقرأ النظام جميع الصور من مجلد `persons/`
2. **ترميز الوجوه**: يحول كل وجه إلى ترميز رقمي فريد
3. **التعرف المباشر**: يقارن الوجوه في الكاميرا مع الوجوه المحفوظة
4. **عرض النتائج**: يظهر اسم الشخص ونسبة الدقة

## استكشاف الأخطاء

### مشاكل شائعة:
- **"لا يمكن فتح الكاميرا"**: تأكد من توصيل الكاميرا وعدم استخدامها من برنامج آخر
- **"مجلد persons غير موجود"**: أنشئ مجلد `persons` وضع فيه الصور
- **"لم يتم العثور على وجه"**: تأكد من وضوح الوجه في الصورة

### نصائح للحصول على أفضل النتائج:
- استخدم صور واضحة وعالية الجودة
- تأكد من إضاءة جيدة عند التصوير
- ضع صورة واحدة لكل شخص
- تجنب الصور التي تحتوي على عدة وجوه

## المطورون
مشروع تعليمي لتعلم تقنيات الذكاء الاصطناعي والتعرف على الوجوه.

## الترخيص
هذا المشروع مفتوح المصدر للأغراض التعليمية.
