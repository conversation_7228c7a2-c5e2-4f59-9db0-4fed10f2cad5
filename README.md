# 🎯 نظام التعرف على الوجوه الاحترافي
## Professional Face Recognition System

<div align="center">

![Python](https://img.shields.io/badge/Python-3.7+-blue.svg)
![OpenCV](https://img.shields.io/badge/OpenCV-4.12+-green.svg)
![Flask](https://img.shields.io/badge/Flask-2.3+-red.svg)
![License](https://img.shields.io/badge/License-MIT-yellow.svg)

**نظام متقدم للتعرف على الوجوه مع ميزات الأمان والمراقبة وواجهة ويب احترافية**

</div>

---

## 📋 وصف المشروع

نظام التعرف على الوجوه الاحترافي هو حل متكامل وشامل يوفر تقنيات متقدمة للتعرف على الوجوه في الوقت الفعلي مع ميزات أمان متطورة وواجهة إدارة سهلة الاستخدام.

## ✨ المميزات الرئيسية

### 🔍 التعرف على الوجوه
- ✅ **التعرف المباشر**: تعرف فوري على الوجوه بدقة عالية
- ✅ **عرض نسبة الثقة**: إظهار مستوى الدقة لكل تعرف
- ✅ **دعم صيغ متعددة**: JPG, PNG, JPEG
- ✅ **معالجة متعددة الخيوط**: أداء محسن وسرعة عالية

### 🛡️ نظام الأمان المتقدم
- 🔒 **كشف التلاعب**: اكتشاف محاولات استخدام صور مطبوعة
- 📹 **تسجيل الفيديو**: تسجيل تلقائي عند اكتشاف الوجوه
- 🚨 **نظام التنبيهات**: تنبيهات فورية للوجوه غير المعروفة
- 📊 **مراقبة الأمان**: مراقبة مستمرة لحالة النظام

### 🎨 واجهة مستخدم محسنة
- 🖥️ **عرض تفاعلي**: واجهة غنية بالمعلومات والإحصائيات
- ⌨️ **اختصارات لوحة المفاتيح**: تحكم سريع وسهل
- 📈 **مؤشرات الأداء**: عرض FPS واستخدام الموارد
- 🎯 **أوضاع متعددة**: وضع عادي، تصحيح، ومراقبة الأداء

### 🌐 واجهة ويب احترافية
- 📊 **لوحة تحكم شاملة**: مراقبة النظام في الوقت الفعلي
- 👥 **إدارة الأشخاص**: إضافة وتعديل وحذف الأشخاص
- ⚙️ **إعدادات متقدمة**: تخصيص شامل لجميع جوانب النظام
- 📋 **السجلات والتقارير**: عرض مفصل للأنشطة والإحصائيات

### 🗄️ قاعدة بيانات متقدمة
- 💾 **SQLite مدمجة**: تخزين آمن وموثوق للبيانات
- 📝 **سجل شامل**: تتبع جميع أحداث التعرف
- 📊 **إحصائيات مفصلة**: تحليل شامل للاستخدام والأداء
- 🔄 **نسخ احتياطية**: حماية البيانات المهمة

## 🏗️ هيكل المشروع الاحترافي

```
Face-recognition-python-project/
├── 📁 src/                          # الكود المصدري
│   ├── 📁 core/                     # النظام الأساسي
│   │   ├── face_recognition_system.py
│   │   ├── face_encoder.py
│   │   ├── performance_monitor.py
│   │   └── security_manager.py
│   ├── 📁 config/                   # الإعدادات
│   │   └── settings.py
│   ├── 📁 database/                 # قاعدة البيانات
│   │   └── database_manager.py
│   ├── 📁 ui/                       # واجهة المستخدم
│   │   └── enhanced_display.py
│   ├── 📁 utils/                    # أدوات مساعدة
│   │   └── logger.py
│   └── 📁 web/                      # واجهة الويب
│       ├── web_server.py
│       └── 📁 templates/
├── 📁 data/                         # البيانات
│   ├── 📁 persons/                  # صور الأشخاص
│   ├── 📁 logs/                     # ملفات السجل
│   ├── 📁 videos/                   # تسجيلات الفيديو
│   └── 📁 encodings/                # ترميزات الوجوه
├── 📁 config/                       # ملفات الإعدادات
│   └── settings.json
├── main.py                          # التطبيق الرئيسي
├── web_app.py                       # تطبيق الويب
├── requirements.txt                 # المتطلبات
└── README.md                        # التوثيق
```

## 🚀 التثبيت والإعداد

### المتطلبات الأساسية
- **Python 3.7+**
- **كاميرا ويب** متصلة بالجهاز
- **نظام التشغيل**: Windows, macOS, Linux

### 1️⃣ تنزيل المشروع
```bash
git clone https://github.com/your-repo/face-recognition-system.git
cd face-recognition-system
```

### 2️⃣ تثبيت المكتبات
```bash
# تثبيت جميع المتطلبات
pip install -r requirements.txt

# أو تثبيت المكتبات منفردة
pip install opencv-python numpy face-recognition psutil GPUtil flask flask-cors flask-socketio
```

### 3️⃣ إعداد البيانات الأولية
```bash
# إنشاء المجلدات المطلوبة (يتم تلقائياً)
# إضافة صور الأشخاص في مجلد data/persons/
```

## 🎮 طرق التشغيل

### 🖥️ التطبيق المكتبي (الواجهة التقليدية)
```bash
python main.py
```

**اختصارات لوحة المفاتيح:**
- `Q` أو `ESC`: الخروج من البرنامج
- `M`: إظهار/إخفاء قائمة التحكم
- `S`: إظهار/إخفاء لوحة الإحصائيات
- `H`: إظهار/إخفاء المساعدة
- `D`: تبديل وضع التصحيح
- `P`: تبديل وضع مراقبة الأداء
- `R`: إعادة تعيين الإحصائيات
- `C`: التقاط لقطة شاشة

### 🌐 واجهة الويب (موصى بها)
```bash
python web_app.py
```

ثم افتح المتصفح على: **http://localhost:5000**

**الصفحات المتاحة:**
- 🏠 **الصفحة الرئيسية**: `/` - نظرة عامة على النظام
- 📊 **لوحة التحكم**: `/dashboard` - مراقبة مباشرة
- 👥 **إدارة الأشخاص**: `/persons` - إضافة وتعديل الأشخاص
- ⚙️ **الإعدادات**: `/settings` - تخصيص النظام
- 📋 **السجلات**: `/logs` - عرض الأنشطة والتقارير

## ⚙️ الإعدادات والتخصيص

### ملف الإعدادات (`config/settings.json`)
```json
{
  "camera": {
    "camera_index": 0,
    "resolution_width": 1280,
    "resolution_height": 720,
    "fps": 30
  },
  "recognition": {
    "confidence_threshold": 0.6,
    "face_detection_model": "hog",
    "max_faces_per_frame": 10
  },
  "security": {
    "enable_recording": false,
    "enable_alerts": true,
    "enable_face_spoofing_detection": false
  },
  "performance": {
    "enable_multithreading": true,
    "max_worker_threads": 4,
    "image_resize_factor": 0.25
  }
}
```

### إضافة أشخاص جدد

#### الطريقة الأولى: عبر واجهة الويب
1. انتقل إلى صفحة "إدارة الأشخاص"
2. اضغط "إضافة شخص جديد"
3. املأ البيانات وارفع الصورة
4. احفظ البيانات

#### الطريقة الثانية: يدوياً
1. ضع صورة الشخص في مجلد `data/persons/`
2. اسم الملف = اسم الشخص (مثال: `ahmed.jpg`)
3. أعد تشغيل النظام لتحديث البيانات

## 📊 مراقبة الأداء والإحصائيات

### مؤشرات الأداء المباشرة
- **FPS**: عدد الإطارات في الثانية
- **استخدام المعالج**: نسبة استخدام CPU
- **استخدام الذاكرة**: نسبة استخدام RAM
- **الوجوه المكتشفة**: عدد الوجوه في الإطار الحالي

### الإحصائيات التراكمية
- **إجمالي الإطارات المعالجة**
- **إجمالي الوجوه المكتشفة**
- **معدل التعرف الناجح**
- **وقت تشغيل النظام**

## 🛡️ ميزات الأمان

### كشف التلاعب
- **اكتشاف الصور المطبوعة**: منع استخدام صور مطبوعة للخداع
- **تحليل جودة الصورة**: فحص وضوح وتباين الصورة
- **تنبيهات فورية**: إشعارات عند اكتشاف محاولات تلاعب

### التسجيل والمراقبة
- **تسجيل تلقائي**: حفظ مقاطع فيديو عند اكتشاف الوجوه
- **حفظ الوجوه غير المعروفة**: أرشفة صور الوجوه غير المسجلة
- **سجل شامل**: تتبع جميع الأحداث مع الطوابع الزمنية

## 🔧 استكشاف الأخطاء

### مشاكل شائعة وحلولها

#### ❌ "لا يمكن فتح الكاميرا"
**الحلول:**
- تأكد من توصيل الكاميرا بشكل صحيح
- أغلق أي تطبيقات أخرى تستخدم الكاميرا
- جرب تغيير `camera_index` في الإعدادات (0, 1, 2...)
- تحقق من تعريفات الكاميرا

#### ❌ "فشل في تنزيل face-recognition"
**الحلول:**
- استخدم Anaconda: `conda install -c conda-forge face_recognition`
- تأكد من تثبيت Visual Studio Build Tools على Windows
- جرب تنزيل dlib منفصلاً: `pip install dlib`

#### ❌ "لم يتم العثور على وجه في الصورة"
**الحلول:**
- استخدم صور عالية الجودة ووضوح جيد
- تأكد من وجود إضاءة كافية في الصورة
- تجنب الصور التي تحتوي على عدة وجوه
- استخدم صور بحجم مناسب (لا تقل عن 200x200 بكسل)

#### ❌ "بطء في الأداء"
**الحلول:**
- قلل `image_resize_factor` في الإعدادات
- فعل `enable_multithreading`
- قلل `max_faces_per_frame`
- استخدم نموذج `hog` بدلاً من `cnn`

### نصائح لأفضل النتائج

#### 📸 جودة الصور
- **الدقة**: استخدم صور بدقة عالية (300x300 بكسل على الأقل)
- **الإضاءة**: تأكد من إضاءة متوازنة وواضحة
- **الزاوية**: صور مواجهة مباشرة للكاميرا
- **الخلفية**: خلفية بسيطة وغير مشتتة

#### ⚡ تحسين الأداء
- **الأجهزة القوية**: استخدم معالج قوي وذاكرة كافية
- **إغلاق التطبيقات**: أغلق البرامج غير الضرورية
- **تحديث التعريفات**: تأكد من تحديث تعريفات الكاميرا
- **الشبكة**: اتصال إنترنت مستقر لواجهة الويب

## 📚 دليل API

### نقاط النهاية الرئيسية

#### معلومات النظام
```http
GET /api/status          # حالة النظام
GET /api/statistics      # إحصائيات شاملة
```

#### إدارة الأشخاص
```http
GET    /api/persons           # قائمة الأشخاص
POST   /api/persons           # إضافة شخص جديد
PUT    /api/persons/{id}      # تحديث بيانات شخص
DELETE /api/persons/{id}      # حذف شخص
```

#### السجلات والتقارير
```http
GET /api/recognition-log      # سجل التعرف
GET /api/security/report      # تقرير الأمان
```

#### التحكم في النظام
```http
POST /api/system/start        # بدء تشغيل النظام
POST /api/system/stop         # إيقاف النظام
```

## 🔄 التحديثات والصيانة

### النسخ الاحتياطية التلقائية
- **قاعدة البيانات**: نسخ احتياطية يومية تلقائية
- **الإعدادات**: حفظ تلقائي عند التغيير
- **السجلات**: أرشفة دورية للسجلات القديمة

### تنظيف البيانات
- **الملفات القديمة**: حذف تلقائي للملفات الأقدم من أسبوع
- **السجلات**: تنظيف السجلات القديمة حسب الإعدادات
- **الذاكرة**: تحسين استخدام الذاكرة تلقائياً

## 🤝 المساهمة والتطوير

### هيكل الكود
- **src/core/**: النظام الأساسي والخوارزميات
- **src/web/**: واجهة الويب والـ API
- **src/database/**: إدارة قاعدة البيانات
- **src/utils/**: أدوات مساعدة ومشتركة

### إضافة ميزات جديدة
1. **فرع جديد**: أنشئ فرع للميزة الجديدة
2. **التطوير**: اتبع معايير الكود الموجودة
3. **الاختبار**: اختبر الميزة بشكل شامل
4. **التوثيق**: حدث التوثيق والـ README
5. **Pull Request**: أرسل طلب دمج

## 📄 الترخيص والحقوق

هذا المشروع مرخص تحت رخصة MIT - انظر ملف [LICENSE](LICENSE) للتفاصيل.

### الاستخدام التجاري
- ✅ **مسموح**: الاستخدام في المشاريع التجارية
- ✅ **التعديل**: تعديل الكود حسب الحاجة
- ✅ **التوزيع**: إعادة توزيع مع ذكر المصدر
- ❌ **الضمان**: لا يوجد ضمان صريح أو ضمني

## 👨‍💻 المطورون والمساهمون

### الفريق الأساسي
- **المطور الرئيسي**: [اسم المطور]
- **مطور الواجهة**: [اسم المطور]
- **مطور الأمان**: [اسم المطور]

### المساهمون
شكر خاص لجميع المساهمين في تطوير هذا المشروع.

## 📞 الدعم والتواصل

### الحصول على المساعدة
- **GitHub Issues**: لتقارير الأخطاء والاقتراحات
- **البريد الإلكتروني**: [<EMAIL>]
- **التوثيق**: راجع هذا الملف والتعليقات في الكود

### تقارير الأخطاء
عند الإبلاغ عن خطأ، يرجى تضمين:
- **نظام التشغيل** وإصدار Python
- **رسالة الخطأ** كاملة
- **خطوات إعادة الإنتاج**
- **ملفات السجل** ذات الصلة

## 🎯 خارطة الطريق

### الإصدارات القادمة

#### v2.1.0 - تحسينات الأداء
- [ ] دعم GPU للمعالجة السريعة
- [ ] تحسين خوارزميات التعرف
- [ ] واجهة موبايل مخصصة

#### v2.2.0 - ميزات متقدمة
- [ ] التعرف على المشاعر
- [ ] تقدير العمر والجنس
- [ ] دعم كاميرات متعددة

#### v3.0.0 - الذكاء الاصطناعي
- [ ] تعلم آلي متقدم
- [ ] تحسين تلقائي للنماذج
- [ ] تكامل مع خدمات السحابة

---

<div align="center">

**🌟 إذا أعجبك هذا المشروع، لا تنس إعطاؤه نجمة على GitHub! 🌟**

[![GitHub stars](https://img.shields.io/github/stars/your-repo/face-recognition-system.svg?style=social&label=Star)](https://github.com/your-repo/face-recognition-system)
[![GitHub forks](https://img.shields.io/github/forks/your-repo/face-recognition-system.svg?style=social&label=Fork)](https://github.com/your-repo/face-recognition-system/fork)

**صنع بـ ❤️ للمجتمع العربي التقني**

</div>
