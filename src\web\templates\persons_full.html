{% extends "base.html" %}

{% block title %}إدارة الأشخاص - نظام التعرف على الوجوه{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h2><i class="bi bi-people me-2"></i>إدارة الأشخاص</h2>
    <div>
        <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addPersonModal">
            <i class="bi bi-person-plus"></i> إضافة شخص جديد
        </button>
        <button class="btn btn-secondary ms-2" onclick="loadPersons()">
            <i class="bi bi-arrow-clockwise"></i> تحديث القائمة
        </button>
    </div>
</div>

<!-- Search and Filter -->
<div class="card mb-4">
    <div class="card-body">
        <div class="row">
            <div class="col-md-6">
                <div class="input-group">
                    <span class="input-group-text"><i class="bi bi-search"></i></span>
                    <input type="text" class="form-control" id="search-input" placeholder="البحث عن شخص...">
                </div>
            </div>
            <div class="col-md-3">
                <select class="form-select" id="department-filter">
                    <option value="">جميع الأقسام</option>
                </select>
            </div>
            <div class="col-md-3">
                <select class="form-select" id="status-filter">
                    <option value="">جميع الحالات</option>
                    <option value="active">نشط</option>
                    <option value="inactive">غير نشط</option>
                </select>
            </div>
        </div>
    </div>
</div>

<!-- Persons Table -->
<div class="card">
    <div class="card-header">
        <h5 class="mb-0"><i class="bi bi-table me-2"></i>قائمة الأشخاص</h5>
    </div>
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-hover table-custom">
                <thead class="table-dark">
                    <tr>
                        <th>الصورة</th>
                        <th>الاسم</th>
                        <th>الاسم الكامل</th>
                        <th>القسم</th>
                        <th>البريد الإلكتروني</th>
                        <th>الهاتف</th>
                        <th>تاريخ الإضافة</th>
                        <th>الحالة</th>
                        <th>الإجراءات</th>
                    </tr>
                </thead>
                <tbody id="persons-table">
                    <tr>
                        <td colspan="9" class="text-center">
                            <div class="spinner-border text-primary" role="status">
                                <span class="visually-hidden">جاري التحميل...</span>
                            </div>
                            <div class="mt-2">جاري تحميل البيانات...</div>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>
</div>

<!-- Add Person Modal -->
<div class="modal fade" id="addPersonModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title"><i class="bi bi-person-plus me-2"></i>إضافة شخص جديد</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="add-person-form">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="person-name" class="form-label">الاسم المختصر *</label>
                                <input type="text" class="form-control" id="person-name" required>
                                <div class="form-text">سيتم استخدامه في التعرف</div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="person-full-name" class="form-label">الاسم الكامل</label>
                                <input type="text" class="form-control" id="person-full-name">
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="person-email" class="form-label">البريد الإلكتروني</label>
                                <input type="email" class="form-control" id="person-email">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="person-phone" class="form-label">رقم الهاتف</label>
                                <input type="tel" class="form-control" id="person-phone">
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="person-department" class="form-label">القسم</label>
                                <input type="text" class="form-control" id="person-department">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="person-position" class="form-label">المنصب</label>
                                <input type="text" class="form-control" id="person-position">
                            </div>
                        </div>
                    </div>
                    <div class="mb-3">
                        <label for="person-notes" class="form-label">ملاحظات</label>
                        <textarea class="form-control" id="person-notes" rows="3"></textarea>
                    </div>
                    <div class="mb-3">
                        <label for="person-image" class="form-label">صورة الشخص</label>
                        <input type="file" class="form-control" id="person-image" accept="image/*" onchange="previewImage(this)">
                        <div class="form-text">اختر صورة واضحة للوجه (JPG, PNG)</div>
                        <div id="image-preview" class="mt-2" style="display: none;">
                            <img id="preview-img" src="" alt="معاينة الصورة" class="img-thumbnail" style="max-width: 200px; max-height: 200px;">
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <button type="button" class="btn btn-primary" id="save-person-btn" onclick="savePerson()">
                    <span class="loading-spinner spinner-border spinner-border-sm me-2" role="status" style="display: none;"></span>
                    حفظ
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    let personsData = [];
    let filteredPersons = [];
    
    function formatDateTime(dateString) {
        if (!dateString) return '-';
        try {
            const date = new Date(dateString);
            return date.toLocaleDateString('ar-SA') + ' ' + date.toLocaleTimeString('ar-SA', {hour: '2-digit', minute: '2-digit'});
        } catch (e) {
            return dateString;
        }
    }
    
    function previewImage(input) {
        const preview = document.getElementById('image-preview');
        const previewImg = document.getElementById('preview-img');
        
        if (input.files && input.files[0]) {
            const reader = new FileReader();
            
            reader.onload = function(e) {
                previewImg.src = e.target.result;
                preview.style.display = 'block';
            };
            
            reader.readAsDataURL(input.files[0]);
        } else {
            preview.style.display = 'none';
        }
    }
    
    async function loadPersons() {
        const tbody = document.getElementById('persons-table');
        
        try {
            console.log('🔄 بدء تحميل الأشخاص...');
            
            // عرض مؤشر التحميل
            tbody.innerHTML = `
                <tr>
                    <td colspan="9" class="text-center">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">جاري التحميل...</span>
                        </div>
                        <div class="mt-2">جاري تحميل البيانات...</div>
                    </td>
                </tr>
            `;
            
            const response = await fetch('/api/persons');
            console.log('📡 استجابة الخادم:', response.status, response.statusText);
            
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }
            
            personsData = await response.json();
            console.log('✅ تم تحميل البيانات:', personsData.length, 'شخص');
            
            if (!Array.isArray(personsData)) {
                throw new Error('البيانات المستلمة ليست مصفوفة');
            }
            
            filteredPersons = [...personsData];
            renderPersonsTable();
            populateFilters();
            
        } catch (error) {
            console.error('❌ خطأ في تحميل الأشخاص:', error);
            tbody.innerHTML = `
                <tr>
                    <td colspan="9" class="text-center text-danger">
                        <i class="bi bi-exclamation-triangle me-2"></i>
                        فشل في تحميل البيانات: ${error.message}
                        <br>
                        <button class="btn btn-sm btn-outline-primary mt-2" onclick="loadPersons()">
                            <i class="bi bi-arrow-clockwise"></i> إعادة المحاولة
                        </button>
                    </td>
                </tr>
            `;
        }
    }
    
    function renderPersonsTable() {
        const tbody = document.getElementById('persons-table');
        
        if (filteredPersons.length === 0) {
            tbody.innerHTML = '<tr><td colspan="9" class="text-center text-muted">لا توجد بيانات</td></tr>';
            return;
        }
        
        tbody.innerHTML = filteredPersons.map(person => `
            <tr>
                <td>
                    <div class="d-flex align-items-center justify-content-center">
                        ${person.image_path ? 
                            `<img src="/static/persons/${person.image_path.split('/').pop()}" 
                                  alt="${person.name}" 
                                  class="rounded-circle" 
                                  style="width: 40px; height: 40px; object-fit: cover;"
                                  onerror="this.style.display='none'; this.nextElementSibling.style.display='flex';">
                             <div class="bg-primary text-white rounded-circle d-flex align-items-center justify-content-center" 
                                  style="width: 40px; height: 40px; font-size: 18px; display: none;">
                                ${person.name.charAt(0).toUpperCase()}
                             </div>` :
                            `<div class="bg-primary text-white rounded-circle d-flex align-items-center justify-content-center" 
                                  style="width: 40px; height: 40px; font-size: 18px;">
                                ${person.name.charAt(0).toUpperCase()}
                             </div>`
                        }
                    </div>
                </td>
                <td><strong>${person.name}</strong></td>
                <td>${person.full_name || '-'}</td>
                <td>${person.department || '-'}</td>
                <td>${person.email || '-'}</td>
                <td>${person.phone || '-'}</td>
                <td>${formatDateTime(person.created_at)}</td>
                <td>
                    <span class="badge ${person.is_active ? 'bg-success' : 'bg-secondary'}">
                        ${person.is_active ? 'نشط' : 'غير نشط'}
                    </span>
                </td>
                <td>
                    <div class="btn-group btn-group-sm">
                        <button class="btn btn-outline-primary" onclick="editPerson(${person.id})" title="تعديل">
                            <i class="bi bi-pencil"></i>
                        </button>
                        <button class="btn btn-outline-danger" onclick="deletePerson(${person.id})" title="حذف">
                            <i class="bi bi-trash"></i>
                        </button>
                    </div>
                </td>
            </tr>
        `).join('');
    }
    
    function populateFilters() {
        const departments = [...new Set(personsData.map(p => p.department).filter(d => d))];
        const departmentFilter = document.getElementById('department-filter');
        
        departmentFilter.innerHTML = '<option value="">جميع الأقسام</option>' +
            departments.map(dept => `<option value="${dept}">${dept}</option>`).join('');
    }
    
    function filterPersons() {
        const searchTerm = document.getElementById('search-input').value.toLowerCase();
        const departmentFilter = document.getElementById('department-filter').value;
        const statusFilter = document.getElementById('status-filter').value;
        
        filteredPersons = personsData.filter(person => {
            const matchesSearch = !searchTerm || 
                person.name.toLowerCase().includes(searchTerm) ||
                (person.full_name && person.full_name.toLowerCase().includes(searchTerm)) ||
                (person.email && person.email.toLowerCase().includes(searchTerm));
            
            const matchesDepartment = !departmentFilter || person.department === departmentFilter;
            
            const matchesStatus = !statusFilter || 
                (statusFilter === 'active' && person.is_active) ||
                (statusFilter === 'inactive' && !person.is_active);
            
            return matchesSearch && matchesDepartment && matchesStatus;
        });
        
        renderPersonsTable();
    }
    
    async function savePerson() {
        const btn = document.getElementById('save-person-btn');
        const spinner = btn.querySelector('.loading-spinner');
        
        btn.disabled = true;
        spinner.style.display = 'inline-block';
        
        try {
            // إنشاء FormData لدعم رفع الملفات
            const formData = new FormData();
            formData.append('name', document.getElementById('person-name').value);
            formData.append('full_name', document.getElementById('person-full-name').value);
            formData.append('email', document.getElementById('person-email').value);
            formData.append('phone', document.getElementById('person-phone').value);
            formData.append('department', document.getElementById('person-department').value);
            formData.append('position', document.getElementById('person-position').value);
            formData.append('notes', document.getElementById('person-notes').value);
            
            // إضافة الصورة إذا تم اختيارها
            const imageFile = document.getElementById('person-image').files[0];
            if (imageFile) {
                formData.append('image', imageFile);
            }
            
            // التحقق من البيانات المطلوبة
            if (!formData.get('name')) {
                showAlert('الاسم مطلوب', 'warning');
                return;
            }
            
            const response = await fetch('/api/persons', {
                method: 'POST',
                body: formData
            });
            
            const result = await response.json();
            
            if (response.ok) {
                showAlert(result.message, 'success');
                bootstrap.Modal.getInstance(document.getElementById('addPersonModal')).hide();
                document.getElementById('add-person-form').reset();
                document.getElementById('image-preview').style.display = 'none';
                loadPersons();
            } else {
                showAlert(result.error || 'فشل في إضافة الشخص', 'danger');
            }
            
        } catch (error) {
            console.error('Error adding person:', error);
            showAlert('فشل في إضافة الشخص', 'danger');
        } finally {
            btn.disabled = false;
            spinner.style.display = 'none';
        }
    }
    
    function editPerson(personId) {
        showAlert('ميزة التعديل ستكون متاحة قريباً', 'info');
    }
    
    function deletePerson(personId) {
        if (confirm('هل أنت متأكد من حذف هذا الشخص؟')) {
            showAlert('ميزة الحذف ستكون متاحة قريباً', 'info');
        }
    }
    
    // تحميل تلقائي عند تحميل الصفحة
    document.addEventListener('DOMContentLoaded', function() {
        console.log('🚀 صفحة الأشخاص تم تحميلها');
        
        // التحقق من وجود العناصر المطلوبة
        const personsTable = document.getElementById('persons-table');
        if (!personsTable) {
            console.error('❌ لم يتم العثور على جدول الأشخاص!');
            return;
        }
        
        console.log('✅ تم العثور على جدول الأشخاص');
        
        // ربط أحداث الفلترة
        const searchInput = document.getElementById('search-input');
        const departmentFilter = document.getElementById('department-filter');
        const statusFilter = document.getElementById('status-filter');
        
        if (searchInput) searchInput.addEventListener('input', filterPersons);
        if (departmentFilter) departmentFilter.addEventListener('change', filterPersons);
        if (statusFilter) statusFilter.addEventListener('change', filterPersons);
        
        // تحميل الأشخاص بعد ثانية واحدة
        setTimeout(() => {
            console.log('⏰ بدء التحميل التلقائي...');
            loadPersons();
        }, 1000);
    });
</script>
{% endblock %}
