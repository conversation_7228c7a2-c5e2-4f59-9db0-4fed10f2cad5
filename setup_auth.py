#!/usr/bin/env python3
"""
إعداد نظام المصادقة وإنشاء المستخدم الافتراضي
Setup Authentication System and Create Default User
"""

import sys
import os

# إضافة مسار المشروع إلى Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from src.database.database_manager import DatabaseManager
from src.config.settings import Settings
from src.auth.auth_manager import AuthManager
from src.utils.logger import get_logger

def setup_authentication():
    """إعداد نظام المصادقة"""
    try:
        logger = get_logger()
        logger.info("🔐 بدء إعداد نظام المصادقة...")
        
        # تحميل الإعدادات
        settings = Settings()
        
        # إنشاء قاعدة البيانات
        db = DatabaseManager(settings.database.database_path)
        
        # إنشاء نظام المصادقة (سيقوم بإنشاء المستخدم الافتراضي تلقائياً)
        auth = AuthManager(db, logger)
        
        # التحقق من المستخدم الافتراضي
        admin_user = db.get_user_by_national_id("99151413104")
        
        if admin_user:
            logger.info("✅ تم إعداد نظام المصادقة بنجاح!")
            logger.info("📋 معلومات المستخدم الافتراضي:")
            logger.info(f"   👤 الاسم: {admin_user['username']}")
            logger.info(f"   🆔 رقم الهوية: {admin_user['national_id']}")
            logger.info(f"   🔑 كلمة المرور: admin")
            logger.info(f"   🛡️ الصلاحيات: {admin_user['role']}")
            logger.info(f"   📅 تاريخ الإنشاء: {admin_user['created_at']}")
            
            print("\n" + "="*60)
            print("🎉 تم إعداد نظام المصادقة بنجاح!")
            print("="*60)
            print("📋 بيانات تسجيل الدخول:")
            print(f"   👤 رقم الهوية: {admin_user['national_id']}")
            print(f"   👤 اسم المستخدم: {admin_user['username']}")
            print(f"   🔑 كلمة المرور: admin")
            print("="*60)
            print("🌐 للوصول إلى النظام:")
            print("   1. شغل الخادم: python web_app.py")
            print("   2. افتح المتصفح: http://localhost:5000")
            print("   3. سجل الدخول بالبيانات أعلاه")
            print("="*60)
            
        else:
            logger.error("❌ فشل في إنشاء المستخدم الافتراضي")
            return False
        
        # اختبار المصادقة
        logger.info("🧪 اختبار نظام المصادقة...")
        
        # اختبار بالرقم الوطني
        test_user = auth.authenticate_user("99151413104", "admin")
        if test_user:
            logger.info("✅ اختبار المصادقة برقم الهوية: نجح")
        else:
            logger.error("❌ اختبار المصادقة برقم الهوية: فشل")
        
        # اختبار باسم المستخدم
        test_user = auth.authenticate_user("admin", "admin")
        if test_user:
            logger.info("✅ اختبار المصادقة باسم المستخدم: نجح")
        else:
            logger.error("❌ اختبار المصادقة باسم المستخدم: فشل")
        
        # عرض جميع المستخدمين
        all_users = db.get_all_users()
        logger.info(f"📊 إجمالي المستخدمين في النظام: {len(all_users)}")
        
        for user in all_users:
            logger.info(f"   👤 {user['username']} ({user['national_id']}) - {user['role']}")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ خطأ في إعداد نظام المصادقة: {e}")
        return False

def test_database_tables():
    """اختبار جداول قاعدة البيانات"""
    try:
        logger = get_logger()
        settings = Settings()
        db = DatabaseManager(settings.database.database_path)
        
        logger.info("🔍 اختبار جداول قاعدة البيانات...")
        
        # اختبار جدول المستخدمين
        try:
            users = db.get_all_users()
            logger.info(f"✅ جدول المستخدمين: {len(users)} مستخدم")
        except Exception as e:
            logger.error(f"❌ خطأ في جدول المستخدمين: {e}")
        
        # اختبار جدول الأشخاص
        try:
            persons = db.get_all_persons()
            logger.info(f"✅ جدول الأشخاص: {len(persons)} شخص")
        except Exception as e:
            logger.error(f"❌ خطأ في جدول الأشخاص: {e}")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ خطأ في اختبار قاعدة البيانات: {e}")
        return False

if __name__ == "__main__":
    print("🔐 إعداد نظام المصادقة لنظام التعرف على الوجوه")
    print("="*60)
    
    # اختبار قاعدة البيانات
    if test_database_tables():
        print("✅ اختبار قاعدة البيانات: نجح")
    else:
        print("❌ اختبار قاعدة البيانات: فشل")
        sys.exit(1)
    
    # إعداد المصادقة
    if setup_authentication():
        print("✅ إعداد نظام المصادقة: نجح")
        print("\n🎯 النظام جاهز للاستخدام!")
    else:
        print("❌ إعداد نظام المصادقة: فشل")
        sys.exit(1)
