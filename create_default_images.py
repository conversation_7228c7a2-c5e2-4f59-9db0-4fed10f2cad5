#!/usr/bin/env python3
"""
إنشاء صور افتراضية للأشخاص بدون صور
"""

import sys
import os
from PIL import Image, ImageDraw, ImageFont
import random

# إضافة مسار المشروع إلى Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from src.database.database_manager import DatabaseManager
from src.config.settings import Settings

def create_avatar_image(name, size=(200, 200)):
    """إنشاء صورة avatar بسيطة"""
    try:
        # ألوان مختلفة للخلفية
        colors = [
            '#FF6B6B',  # أحمر
            '#4ECDC4',  # تركوازي
            '#45B7D1',  # أزرق
            '#96CEB4',  # أخضر
            '#FFEAA7',  # أصفر
            '#DDA0DD',  # بنفسجي
            '#98D8C8',  # أخضر فاتح
            '#F7DC6F',  # ذهبي
        ]
        
        # اختيار لون عشوائي بناءً على الاسم
        color_index = hash(name) % len(colors)
        bg_color = colors[color_index]
        
        # إنشاء الصورة
        image = Image.new('RGB', size, bg_color)
        draw = ImageDraw.Draw(image)
        
        # الحصول على الحرف الأول
        initial = name[0].upper() if name else '?'
        
        # محاولة استخدام خط عربي أو خط افتراضي
        try:
            # محاولة استخدام خط كبير
            font_size = size[0] // 2
            font = ImageFont.truetype("arial.ttf", font_size)
        except:
            try:
                # محاولة استخدام خط افتراضي
                font = ImageFont.load_default()
            except:
                font = None
        
        # حساب موقع النص
        if font:
            bbox = draw.textbbox((0, 0), initial, font=font)
            text_width = bbox[2] - bbox[0]
            text_height = bbox[3] - bbox[1]
        else:
            text_width = size[0] // 4
            text_height = size[1] // 4
            
        x = (size[0] - text_width) // 2
        y = (size[1] - text_height) // 2
        
        # رسم النص
        draw.text((x, y), initial, fill='white', font=font)
        
        # إضافة دائرة حول الصورة
        draw.ellipse([10, 10, size[0]-10, size[1]-10], outline='white', width=5)
        
        return image
        
    except Exception as e:
        print(f"خطأ في إنشاء صورة {name}: {e}")
        return None

def main():
    """إنشاء صور افتراضية للأشخاص"""
    try:
        settings = Settings()
        db = DatabaseManager(settings.database.database_path)
        persons = db.get_all_persons()
        
        # إنشاء مجلد الصور إذا لم يكن موجوداً
        persons_dir = os.path.join(os.getcwd(), 'data', 'persons')
        os.makedirs(persons_dir, exist_ok=True)
        
        static_persons_dir = os.path.join(os.getcwd(), 'src', 'web', 'static', 'persons')
        os.makedirs(static_persons_dir, exist_ok=True)
        
        print(f"إنشاء صور افتراضية لـ {len(persons)} شخص...")
        print("-" * 50)
        
        created_count = 0
        
        for person in persons:
            name = person.get('name', 'Unknown')
            image_path = person.get('image_path', None)
            
            if not image_path:
                print(f"إنشاء صورة افتراضية لـ: {name}")
                
                # إنشاء الصورة
                avatar = create_avatar_image(name)
                
                if avatar:
                    # حفظ الصورة في مجلد data/persons
                    filename = f"{name}_default.jpg"
                    safe_filename = "".join(c for c in filename if c.isalnum() or c in (' ', '-', '_', '.')).rstrip()
                    
                    data_path = os.path.join(persons_dir, safe_filename)
                    static_path = os.path.join(static_persons_dir, safe_filename)
                    
                    # حفظ في كلا المجلدين
                    avatar.save(data_path, 'JPEG', quality=90)
                    avatar.save(static_path, 'JPEG', quality=90)
                    
                    # تحديث قاعدة البيانات
                    db.update_person(person['id'], image_path=data_path)
                    
                    print(f"✅ تم إنشاء: {safe_filename}")
                    created_count += 1
                else:
                    print(f"❌ فشل في إنشاء صورة لـ: {name}")
            else:
                print(f"⏭️ {name} - يملك صورة بالفعل")
        
        print("-" * 50)
        print(f"✅ تم إنشاء {created_count} صورة افتراضية")
        
    except Exception as e:
        print(f"خطأ: {e}")

if __name__ == "__main__":
    main()
