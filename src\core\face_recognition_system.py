"""
النظام الأساسي للتعرف على الوجوه مع تحسينات الأداء
Advanced Face Recognition System with Performance Optimizations
"""

import cv2
import numpy as np
import face_recognition
import threading
import queue
import time
import pickle
from datetime import datetime
from pathlib import Path
from typing import List, Dict, Tuple, Optional, Any
from concurrent.futures import ThreadPoolExecutor

from ..config.settings import Settings
from ..database.database_manager import DatabaseManager
from ..utils.logger import get_logger
from .face_encoder import FaceEncoder
from .performance_monitor import PerformanceMonitor
from .security_manager import SecurityManager
from ..ui.enhanced_display import EnhancedDisplay


class FaceRecognitionSystem:
    """النظام الرئيسي للتعرف على الوجوه"""
    
    def __init__(self, settings: Settings):
        self.settings = settings
        self.logger = get_logger()
        self.db = DatabaseManager(settings.database.database_path)
        self.face_encoder = FaceEncoder(settings)
        self.performance_monitor = PerformanceMonitor()
        self.security_manager = SecurityManager(settings, self.db)
        self.display = EnhancedDisplay(settings)
        
        # متغيرات النظام
        self.is_running = False
        self.camera = None
        self.known_encodings = []
        self.known_names = []
        self.frame_queue = queue.Queue(maxsize=10)
        self.result_queue = queue.Queue()
        
        # إحصائيات الأداء
        self.frame_count = 0
        self.fps = 0
        self.last_fps_time = time.time()
        
        # خيوط المعالجة
        self.processing_thread = None
        self.display_thread = None
        self.executor = None
        
        self.logger.info("تم تهيئة نظام التعرف على الوجوه")
    
    def initialize(self) -> bool:
        """تهيئة النظام"""
        try:
            # تحميل الوجوه المعروفة
            if not self._load_known_faces():
                self.logger.error("فشل في تحميل الوجوه المعروفة")
                return False
            
            # تهيئة الكاميرا
            if not self._initialize_camera():
                self.logger.error("فشل في تهيئة الكاميرا")
                return False
            
            # تهيئة خيوط المعالجة
            if self.settings.performance.enable_multithreading:
                self.executor = ThreadPoolExecutor(
                    max_workers=self.settings.performance.max_worker_threads
                )
            
            self.logger.info("تم تهيئة النظام بنجاح")
            return True
            
        except Exception as e:
            self.logger.error(f"خطأ في تهيئة النظام: {e}")
            return False
    
    def _initialize_camera(self) -> bool:
        """تهيئة الكاميرا"""
        try:
            self.camera = cv2.VideoCapture(self.settings.camera.camera_index)
            
            if not self.camera.isOpened():
                return False
            
            # تعيين إعدادات الكاميرا
            self.camera.set(cv2.CAP_PROP_FRAME_WIDTH, self.settings.camera.resolution_width)
            self.camera.set(cv2.CAP_PROP_FRAME_HEIGHT, self.settings.camera.resolution_height)
            self.camera.set(cv2.CAP_PROP_FPS, self.settings.camera.fps)
            
            # اختبار قراءة إطار
            ret, frame = self.camera.read()
            if not ret:
                return False
            
            self.logger.log_camera_event("تم تهيئة الكاميرا بنجاح")
            return True
            
        except Exception as e:
            self.logger.error(f"خطأ في تهيئة الكاميرا: {e}")
            return False
    
    def _load_known_faces(self) -> bool:
        """تحميل الوجوه المعروفة"""
        try:
            # محاولة تحميل الترميزات المحفوظة
            encodings_file = Path(self.settings.get_encodings_path())
            
            if encodings_file.exists() and self.settings.performance.cache_encodings:
                self.logger.info("تحميل الترميزات المحفوظة...")
                with open(encodings_file, 'rb') as f:
                    data = pickle.load(f)
                    self.known_encodings = data['encodings']
                    self.known_names = data['names']
                
                if len(self.known_encodings) > 0:
                    self.logger.info(f"تم تحميل {len(self.known_encodings)} ترميز محفوظ")
                    return True
            
            # إنشاء الترميزات من الصور
            self.logger.info("إنشاء ترميزات جديدة من الصور...")
            persons_path = Path(self.settings.get_persons_path())
            
            if not persons_path.exists():
                persons_path.mkdir(parents=True, exist_ok=True)
                self.logger.warning(f"تم إنشاء مجلد الأشخاص: {persons_path}")
            
            # نسخ الصور من المجلد القديم إذا كان موجوداً
            old_persons_path = Path("persons")
            if old_persons_path.exists():
                self._migrate_old_images(old_persons_path, persons_path)
            
            # تحميل الصور وإنشاء الترميزات
            image_files = list(persons_path.glob("*.jpg")) + \
                         list(persons_path.glob("*.jpeg")) + \
                         list(persons_path.glob("*.png"))
            
            if not image_files:
                self.logger.warning("لا توجد صور في مجلد الأشخاص")
                return True  # يمكن المتابعة بدون وجوه معروفة
            
            self.known_encodings, self.known_names = self.face_encoder.encode_faces_from_images(image_files)
            
            # حفظ الترميزات للاستخدام المستقبلي
            if self.settings.performance.cache_encodings and len(self.known_encodings) > 0:
                self._save_encodings()
            
            # تحديث قاعدة البيانات
            self._update_database_with_persons()
            
            self.logger.info(f"تم تحميل {len(self.known_encodings)} وجه معروف")
            return True
            
        except Exception as e:
            self.logger.error(f"خطأ في تحميل الوجوه المعروفة: {e}")
            return False
    
    def _migrate_old_images(self, old_path: Path, new_path: Path):
        """نقل الصور من المجلد القديم"""
        try:
            import shutil
            for image_file in old_path.glob("*"):
                if image_file.suffix.lower() in ['.jpg', '.jpeg', '.png']:
                    new_file = new_path / image_file.name
                    if not new_file.exists():
                        shutil.copy2(image_file, new_file)
                        self.logger.info(f"تم نقل الصورة: {image_file.name}")
        except Exception as e:
            self.logger.error(f"خطأ في نقل الصور: {e}")
    
    def _save_encodings(self):
        """حفظ الترميزات"""
        try:
            encodings_file = Path(self.settings.get_encodings_path())
            encodings_file.parent.mkdir(parents=True, exist_ok=True)
            
            data = {
                'encodings': self.known_encodings,
                'names': self.known_names,
                'timestamp': datetime.now().isoformat()
            }
            
            with open(encodings_file, 'wb') as f:
                pickle.dump(data, f)
            
            self.logger.info(f"تم حفظ الترميزات في: {encodings_file}")
            
        except Exception as e:
            self.logger.error(f"خطأ في حفظ الترميزات: {e}")
    
    def _update_database_with_persons(self):
        """تحديث قاعدة البيانات بالأشخاص"""
        try:
            for i, name in enumerate(self.known_names):
                # التحقق من وجود الشخص في قاعدة البيانات
                person = self.db.get_person_by_name(name)
                
                if not person:
                    # إضافة شخص جديد
                    encoding_data = pickle.dumps(self.known_encodings[i])
                    self.db.add_person(
                        name=name,
                        full_name=name,
                        encoding_data=encoding_data
                    )
                    self.logger.info(f"تم إضافة شخص جديد لقاعدة البيانات: {name}")
                
        except Exception as e:
            self.logger.error(f"خطأ في تحديث قاعدة البيانات: {e}")
    
    def run(self):
        """تشغيل النظام الرئيسي"""
        if not self.initialize():
            self.logger.error("فشل في تهيئة النظام")
            return
        
        self.is_running = True
        self.logger.info("بدء تشغيل نظام التعرف على الوجوه")
        
        try:
            if self.settings.performance.enable_multithreading:
                self._run_multithreaded()
            else:
                self._run_single_threaded()
                
        except KeyboardInterrupt:
            self.logger.info("تم إيقاف النظام بواسطة المستخدم")
        except Exception as e:
            self.logger.error(f"خطأ في تشغيل النظام: {e}")
        finally:
            self.cleanup()
    
    def _run_single_threaded(self):
        """تشغيل النظام بخيط واحد"""
        while self.is_running:
            ret, frame = self.camera.read()
            if not ret:
                self.logger.error("فشل في قراءة الإطار من الكاميرا")
                break
            
            # معالجة الإطار
            processed_frame = self._process_frame(frame)
            
            # تغيير حجم الإطار للعرض
            display_frame = cv2.resize(processed_frame,
                                     (self.settings.ui.window_width, self.settings.ui.window_height))

            # عرض الإطار
            cv2.imshow('نظام التعرف على الوجوه الاحترافي', display_frame)
            
            # التحكم في الخروج
            key = cv2.waitKey(1) & 0xFF
            if key != 255:  # إذا تم الضغط على مفتاح
                action = self.display.handle_key_press(key)
                if action == "quit":
                    break
                elif action == "screenshot":
                    self._save_screenshot()
                elif action == "stats_reset":
                    self.performance_monitor.reset_statistics()
            
            # تحديث FPS
            self._update_fps()
    
    def cleanup(self):
        """تنظيف الموارد"""
        self.is_running = False
        
        if self.camera:
            self.camera.release()
        
        cv2.destroyAllWindows()
        
        if self.executor:
            self.executor.shutdown(wait=True)

        # تنظيف موارد الأمان
        self.security_manager.stop_monitoring()

        self.logger.info("تم تنظيف موارد النظام")

    def _process_frame(self, frame: np.ndarray) -> np.ndarray:
        """معالجة إطار واحد"""
        self.performance_monitor.start_frame_processing()

        try:
            # اكتشاف الوجوه
            face_locations = self.face_encoder.detect_faces_in_frame(frame)
            faces_detected = len(face_locations)
            faces_recognized = 0
            face_data = []

            # معالجة كل وجه
            for face_location in face_locations:
                # ترميز الوجه
                face_encoding = self.face_encoder.encode_face_from_frame(frame, face_location)

                if face_encoding is not None:
                    # البحث عن تطابق
                    name, confidence, is_confident = self.face_encoder.get_best_match(
                        self.known_encodings, self.known_names, face_encoding
                    )

                    if is_confident and name:
                        faces_recognized += 1

                        # إعداد بيانات الوجه
                        face_info = {
                            'location': face_location,
                            'name': name,
                            'confidence': confidence,
                            'is_known': True,
                            'encoding': face_encoding
                        }

                        # تسجيل في قاعدة البيانات
                        person = self.db.get_person_by_name(name)
                        if person:
                            self.db.log_recognition(
                                person['id'], name, confidence,
                                self.settings.camera.camera_index
                            )

                        # تسجيل في السجل
                        self.logger.log_recognition_event(name, confidence)

                    else:
                        # وجه غير معروف
                        face_info = {
                            'location': face_location,
                            'name': 'غير معروف',
                            'confidence': 0,
                            'is_known': False,
                            'encoding': face_encoding
                        }

                        # تسجيل الوجه غير المعروف
                        self.db.log_unknown_face(self.settings.camera.camera_index)
                        self.logger.log_unknown_face()

                    face_data.append(face_info)

            # معالجة الأمان
            security_info = self.security_manager.process_frame_security(frame, face_data)

            # تحديث مراقب الأداء
            self.performance_monitor.end_frame_processing(faces_detected, faces_recognized)

            # الحصول على بيانات الأداء
            performance_data = {
                'fps': self.performance_monitor.get_current_fps(),
                'cpu_usage': 0,  # سيتم تحديثها بواسطة مراقب الأداء
                'memory_usage': 0,
                'processing_time': 0
            }

            # إضافة مقاييس النظام إذا كانت متاحة
            latest_metrics = self.performance_monitor.get_latest_metrics()
            if latest_metrics:
                performance_data.update({
                    'cpu_usage': latest_metrics.cpu_usage,
                    'memory_usage': latest_metrics.memory_usage,
                    'processing_time': latest_metrics.processing_time
                })

            # رسم الإطار المحسن
            enhanced_frame = self.display.draw_enhanced_frame(frame, face_data, performance_data)

            return enhanced_frame

        except Exception as e:
            self.logger.error(f"خطأ في معالجة الإطار: {e}")
            return frame



    def _run_multithreaded(self):
        """تشغيل النظام بخيوط متعددة"""
        # بدء مراقبة الأداء والأمان
        self.performance_monitor.start_monitoring()
        self.security_manager.start_monitoring()

        # بدء خيط المعالجة
        self.processing_thread = threading.Thread(target=self._processing_worker, daemon=True)
        self.processing_thread.start()

        # الحلقة الرئيسية لالتقاط الإطارات
        while self.is_running:
            ret, frame = self.camera.read()
            if not ret:
                self.logger.error("فشل في قراءة الإطار من الكاميرا")
                break

            # إضافة الإطار إلى قائمة الانتظار
            if not self.frame_queue.full():
                self.frame_queue.put(frame)

            # عرض الإطار المعالج
            try:
                processed_frame = self.result_queue.get_nowait()
                display_frame = cv2.resize(processed_frame,
                                         (self.settings.ui.window_width, self.settings.ui.window_height))
                cv2.imshow('نظام التعرف على الوجوه الاحترافي', display_frame)
            except queue.Empty:
                # عرض الإطار الأصلي إذا لم تكن هناك نتائج
                display_frame = cv2.resize(frame,
                                         (self.settings.ui.window_width, self.settings.ui.window_height))
                cv2.imshow('نظام التعرف على الوجوه الاحترافي', display_frame)

            # التحكم في الخروج
            key = cv2.waitKey(1) & 0xFF
            if key != 255:  # إذا تم الضغط على مفتاح
                action = self.display.handle_key_press(key)
                if action == "quit":
                    break
                elif action == "screenshot":
                    self._save_screenshot()
                elif action == "stats_reset":
                    self.performance_monitor.reset_statistics()

        # إيقاف مراقبة الأداء والأمان
        self.performance_monitor.stop_monitoring()
        self.security_manager.stop_monitoring()

    def _processing_worker(self):
        """عامل معالجة الإطارات"""
        while self.is_running:
            try:
                # الحصول على إطار من قائمة الانتظار
                frame = self.frame_queue.get(timeout=1.0)

                # معالجة الإطار
                processed_frame = self._process_frame(frame)

                # إضافة النتيجة إلى قائمة النتائج
                if not self.result_queue.full():
                    self.result_queue.put(processed_frame)

            except queue.Empty:
                continue
            except Exception as e:
                self.logger.error(f"خطأ في عامل المعالجة: {e}")

    def _save_screenshot(self):
        """حفظ لقطة شاشة"""
        try:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            screenshot_path = Path(f"data/screenshots/screenshot_{timestamp}.jpg")
            screenshot_path.parent.mkdir(parents=True, exist_ok=True)

            ret, frame = self.camera.read()
            if ret:
                cv2.imwrite(str(screenshot_path), frame)
                self.logger.info(f"تم حفظ لقطة الشاشة: {screenshot_path}")

        except Exception as e:
            self.logger.error(f"خطأ في حفظ لقطة الشاشة: {e}")

    def add_new_person(self, name: str, image_path: str) -> bool:
        """إضافة شخص جديد"""
        try:
            # قراءة الصورة
            image = cv2.imread(image_path)
            if image is None:
                self.logger.error(f"لا يمكن قراءة الصورة: {image_path}")
                return False

            # التحقق من جودة الصورة
            is_valid, message = self.face_encoder.validate_image_quality(image)
            if not is_valid:
                self.logger.error(f"جودة الصورة غير مناسبة: {message}")
                return False

            # ترميز الوجه
            encodings, names = self.face_encoder.encode_faces_from_images([Path(image_path)])

            if not encodings:
                self.logger.error("لم يتم العثور على وجه في الصورة")
                return False

            # إضافة إلى القوائم المعروفة
            self.known_encodings.append(encodings[0])
            self.known_names.append(name)

            # حفظ في قاعدة البيانات
            encoding_data = pickle.dumps(encodings[0])
            self.db.add_person(name=name, encoding_data=encoding_data, image_path=image_path)

            # حفظ الترميزات المحدثة
            if self.settings.performance.cache_encodings:
                self._save_encodings()

            self.logger.info(f"تم إضافة شخص جديد: {name}")
            return True

        except Exception as e:
            self.logger.error(f"خطأ في إضافة شخص جديد: {e}")
            return False

    def stop_recognition(self):
        """إيقاف نظام التعرف"""
        self.is_running = False
        self.logger.info("تم طلب إيقاف نظام التعرف على الوجوه")

        # إيقاف الكاميرا
        if hasattr(self, 'camera') and self.camera is not None:
            self.camera.release()

        # إغلاق النوافذ
        cv2.destroyAllWindows()

        # إيقاف مراقب الأداء
        if hasattr(self, 'performance_monitor'):
            self.performance_monitor.stop_monitoring()

        # إيقاف نظام الأمان
        if hasattr(self, 'security_manager'):
            self.security_manager.stop_monitoring()

    def get_system_status(self):
        """الحصول على حالة النظام"""
        return {
            'is_running': getattr(self, 'is_running', False),
            'camera_connected': hasattr(self, 'camera') and self.camera is not None,
            'known_persons_count': len(self.known_names),
            'performance_monitoring': hasattr(self, 'performance_monitor'),
            'security_monitoring': hasattr(self, 'security_manager')
        }
