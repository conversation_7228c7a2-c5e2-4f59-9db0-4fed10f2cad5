#!/usr/bin/env python3
"""
تنظيف الصور الافتراضية للأشخاص الذين لديهم صور حقيقية
"""

import sys
import os

# إضافة مسار المشروع إلى Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from src.database.database_manager import DatabaseManager
from src.config.settings import Settings

def clean_default_images():
    """حذف الصور الافتراضية من الملفات (ليس من قاعدة البيانات)"""
    try:
        settings = Settings()
        db = DatabaseManager(settings.database.database_path)
        persons = db.get_all_persons()
        
        persons_dir = os.path.join(os.getcwd(), 'data', 'persons')
        static_dir = os.path.join(os.getcwd(), 'src', 'web', 'static', 'persons')
        
        print("تنظيف الصور الافتراضية...")
        print("-" * 50)
        
        cleaned_count = 0
        
        for person in persons:
            name = person.get('name')
            image_path = person.get('image_path', '')
            
            # إذا كان الشخص لديه صورة حقيقية
            if image_path and '_default.' not in image_path:
                # البحث عن الصورة الافتراضية المقابلة
                default_filename = f"{name}_default.jpg"
                safe_default_filename = "".join(c for c in default_filename if c.isalnum() or c in (' ', '-', '_', '.')).rstrip()
                
                default_data_path = os.path.join(persons_dir, safe_default_filename)
                default_static_path = os.path.join(static_dir, safe_default_filename)
                
                # حذف الصورة الافتراضية من data/persons
                if os.path.exists(default_data_path):
                    os.remove(default_data_path)
                    print(f"🗑️ تم حذف: {default_data_path}")
                    cleaned_count += 1
                
                # حذف الصورة الافتراضية من static/persons
                if os.path.exists(default_static_path):
                    os.remove(default_static_path)
                    print(f"🗑️ تم حذف: {default_static_path}")
                
                print(f"✅ {name}: تم الاحتفاظ بالصورة الحقيقية فقط")
            else:
                print(f"⏭️ {name}: لا توجد صورة حقيقية، تم الاحتفاظ بالصورة الافتراضية")
        
        print("-" * 50)
        print(f"✅ تم تنظيف {cleaned_count} صورة افتراضية")
        
        # عرض النتائج النهائية
        print("\nالصور المتبقية في data/persons:")
        if os.path.exists(persons_dir):
            for file in os.listdir(persons_dir):
                if file.endswith(('.jpg', '.jpeg', '.png')):
                    if '_default.' in file:
                        print(f"🎨 {file} (افتراضية)")
                    else:
                        print(f"📸 {file} (حقيقية)")
        
        print("\nالصور المتبقية في static/persons:")
        if os.path.exists(static_dir):
            for file in os.listdir(static_dir):
                if file.endswith(('.jpg', '.jpeg', '.png')):
                    if '_default.' in file:
                        print(f"🎨 {file} (افتراضية)")
                    else:
                        print(f"📸 {file} (حقيقية)")
                        
    except Exception as e:
        print(f"خطأ: {e}")

if __name__ == "__main__":
    clean_default_images()
