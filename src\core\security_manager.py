"""
نظام الأمان والمراقبة
Security and Monitoring System
"""

import cv2
import numpy as np
import threading
import time
import json
from datetime import datetime, timedelta
from pathlib import Path
from typing import Dict, List, Optional, Callable
import hashlib

from ..config.settings import Settings
from ..database.database_manager import DatabaseManager
from ..utils.logger import get_logger


class SecurityManager:
    """مدير الأمان والمراقبة"""
    
    def __init__(self, settings: Settings, db: DatabaseManager):
        self.settings = settings
        self.db = db
        self.logger = get_logger()
        
        # إعدادات التسجيل
        self.is_recording = False
        self.video_writer = None
        self.recording_start_time = None
        self.current_recording_path = None
        
        # إعدادات التنبيهات
        self.alert_callbacks = []
        self.unknown_face_alerts = []
        self.last_alert_time = {}
        
        # إعدادات كشف التلاعب
        self.face_spoofing_detector = None
        self.enable_spoofing_detection = settings.security.enable_face_spoofing_detection
        
        # خيط المراقبة
        self.monitoring_thread = None
        self.is_monitoring = False
        
        # إحصائيات الأمان
        self.security_stats = {
            'total_alerts': 0,
            'unknown_faces_detected': 0,
            'spoofing_attempts': 0,
            'recordings_created': 0,
            'session_start': datetime.now()
        }
        
        self.logger.info("تم تهيئة نظام الأمان والمراقبة")
    
    def start_monitoring(self):
        """بدء مراقبة الأمان"""
        if not self.is_monitoring:
            self.is_monitoring = True
            self.monitoring_thread = threading.Thread(target=self._security_monitor, daemon=True)
            self.monitoring_thread.start()
            self.logger.log_security_alert("SYSTEM", "تم بدء مراقبة الأمان")
    
    def stop_monitoring(self):
        """إيقاف مراقبة الأمان"""
        self.is_monitoring = False
        if self.monitoring_thread:
            self.monitoring_thread.join(timeout=1.0)
        
        # إيقاف التسجيل إذا كان نشطاً
        if self.is_recording:
            self.stop_recording()
        
        self.logger.log_security_alert("SYSTEM", "تم إيقاف مراقبة الأمان")
    
    def process_frame_security(self, frame: np.ndarray, face_data: List[Dict]) -> Dict:
        """معالجة إطار من ناحية الأمان"""
        security_info = {
            'threats_detected': 0,
            'unknown_faces': 0,
            'spoofing_detected': False,
            'alerts_triggered': []
        }
        
        try:
            # فحص الوجوه غير المعروفة
            unknown_faces = [face for face in face_data if not face.get('is_known', False)]
            security_info['unknown_faces'] = len(unknown_faces)
            
            if unknown_faces:
                self._handle_unknown_faces(frame, unknown_faces, security_info)
            
            # كشف التلاعب إذا كان مفعلاً
            if self.enable_spoofing_detection:
                spoofing_detected = self._detect_face_spoofing(frame, face_data)
                security_info['spoofing_detected'] = spoofing_detected
                
                if spoofing_detected:
                    self._handle_spoofing_attempt(frame, security_info)
            
            # تسجيل الفيديو إذا كان مفعلاً
            if self.settings.security.enable_recording:
                self._handle_video_recording(frame, face_data)
            
            # تحديث الإحصائيات
            self._update_security_stats(security_info)
            
        except Exception as e:
            self.logger.error(f"خطأ في معالجة الأمان: {e}")
        
        return security_info
    
    def _handle_unknown_faces(self, frame: np.ndarray, unknown_faces: List[Dict], 
                            security_info: Dict):
        """معالجة الوجوه غير المعروفة"""
        current_time = datetime.now()
        
        for face in unknown_faces:
            # حفظ صورة الوجه غير المعروف
            face_image_path = self._save_unknown_face_image(frame, face)
            
            # تسجيل في قاعدة البيانات
            self.db.log_unknown_face(
                camera_id=self.settings.camera.camera_index,
                image_path=face_image_path
            )
            
            # إرسال تنبيه إذا لم يتم إرسال تنبيه مؤخراً
            if self._should_send_alert('unknown_face'):
                alert_data = {
                    'type': 'unknown_face',
                    'timestamp': current_time,
                    'location': face['location'],
                    'image_path': face_image_path
                }
                
                self._trigger_alert(alert_data)
                security_info['alerts_triggered'].append(alert_data)
                security_info['threats_detected'] += 1
    
    def _detect_face_spoofing(self, frame: np.ndarray, face_data: List[Dict]) -> bool:
        """كشف محاولات التلاعب بالوجوه"""
        try:
            # خوارزمية بسيطة لكشف التلاعب
            # يمكن تحسينها باستخدام نماذج أكثر تقدماً
            
            for face in face_data:
                location = face['location']
                top, right, bottom, left = location
                
                # استخراج منطقة الوجه
                face_region = frame[top:bottom, left:right]
                
                if face_region.size == 0:
                    continue
                
                # فحص جودة الصورة (الصور المطبوعة عادة أقل جودة)
                gray_face = cv2.cvtColor(face_region, cv2.COLOR_BGR2GRAY)
                
                # حساب التباين (الصور المطبوعة لها تباين أقل)
                variance = cv2.Laplacian(gray_face, cv2.CV_64F).var()
                
                # حساب التدرج (الصور المطبوعة لها تدرجات أقل)
                gradient_x = cv2.Sobel(gray_face, cv2.CV_64F, 1, 0, ksize=3)
                gradient_y = cv2.Sobel(gray_face, cv2.CV_64F, 0, 1, ksize=3)
                gradient_magnitude = np.sqrt(gradient_x**2 + gradient_y**2)
                avg_gradient = np.mean(gradient_magnitude)
                
                # عتبات الكشف (يمكن تحسينها)
                if variance < 50 or avg_gradient < 20:
                    return True
            
            return False
            
        except Exception as e:
            self.logger.error(f"خطأ في كشف التلاعب: {e}")
            return False
    
    def _handle_spoofing_attempt(self, frame: np.ndarray, security_info: Dict):
        """معالجة محاولة التلاعب"""
        current_time = datetime.now()
        
        # حفظ صورة محاولة التلاعب
        spoofing_image_path = self._save_spoofing_attempt_image(frame)
        
        # تسجيل في قاعدة البيانات
        self.db.log_system_event(
            event_type="SPOOFING_ATTEMPT",
            event_data=f"محاولة تلاعب مكتشفة - الصورة: {spoofing_image_path}",
            severity="WARNING"
        )
        
        # إرسال تنبيه فوري
        alert_data = {
            'type': 'spoofing_attempt',
            'timestamp': current_time,
            'image_path': spoofing_image_path,
            'severity': 'HIGH'
        }
        
        self._trigger_alert(alert_data)
        security_info['alerts_triggered'].append(alert_data)
        security_info['threats_detected'] += 1
        
        self.logger.log_security_alert("SPOOFING", f"محاولة تلاعب مكتشفة: {spoofing_image_path}")
    
    def _handle_video_recording(self, frame: np.ndarray, face_data: List[Dict]):
        """معالجة تسجيل الفيديو"""
        should_record = len(face_data) > 0  # تسجيل عند وجود وجوه
        
        if should_record and not self.is_recording:
            self.start_recording()
        elif not should_record and self.is_recording:
            # إيقاف التسجيل بعد فترة من عدم وجود وجوه
            if self.recording_start_time and \
               (datetime.now() - self.recording_start_time).seconds > 5:
                self.stop_recording()
        
        if self.is_recording and self.video_writer:
            self.video_writer.write(frame)
    
    def start_recording(self):
        """بدء تسجيل الفيديو"""
        if self.is_recording:
            return
        
        try:
            # إنشاء مسار التسجيل
            recordings_dir = Path(self.settings.security.recording_path)
            recordings_dir.mkdir(parents=True, exist_ok=True)
            
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            self.current_recording_path = recordings_dir / f"recording_{timestamp}.mp4"
            
            # إعداد كاتب الفيديو
            fourcc = cv2.VideoWriter_fourcc(*'mp4v')
            self.video_writer = cv2.VideoWriter(
                str(self.current_recording_path),
                fourcc,
                self.settings.camera.fps,
                (self.settings.camera.resolution_width, 
                 self.settings.camera.resolution_height)
            )
            
            self.is_recording = True
            self.recording_start_time = datetime.now()
            self.security_stats['recordings_created'] += 1
            
            self.logger.log_security_alert("RECORDING", f"بدء التسجيل: {self.current_recording_path}")
            
        except Exception as e:
            self.logger.error(f"خطأ في بدء التسجيل: {e}")
    
    def stop_recording(self):
        """إيقاف تسجيل الفيديو"""
        if not self.is_recording:
            return
        
        try:
            if self.video_writer:
                self.video_writer.release()
                self.video_writer = None
            
            self.is_recording = False
            recording_duration = datetime.now() - self.recording_start_time
            
            # تسجيل معلومات التسجيل في قاعدة البيانات
            self.db.log_system_event(
                event_type="VIDEO_RECORDING",
                event_data=json.dumps({
                    'file_path': str(self.current_recording_path),
                    'duration_seconds': recording_duration.total_seconds(),
                    'start_time': self.recording_start_time.isoformat()
                }),
                severity="INFO"
            )
            
            self.logger.log_security_alert("RECORDING", 
                f"انتهاء التسجيل: {self.current_recording_path} - المدة: {recording_duration}")
            
        except Exception as e:
            self.logger.error(f"خطأ في إيقاف التسجيل: {e}")
    
    def _save_unknown_face_image(self, frame: np.ndarray, face: Dict) -> str:
        """حفظ صورة وجه غير معروف"""
        try:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S_%f")[:-3]
            unknown_faces_dir = Path("data/unknown_faces")
            unknown_faces_dir.mkdir(parents=True, exist_ok=True)
            
            # استخراج منطقة الوجه
            location = face['location']
            top, right, bottom, left = location
            
            # إضافة هامش حول الوجه
            margin = 20
            top = max(0, top - margin)
            left = max(0, left - margin)
            bottom = min(frame.shape[0], bottom + margin)
            right = min(frame.shape[1], right + margin)
            
            face_image = frame[top:bottom, left:right]
            
            image_path = unknown_faces_dir / f"unknown_{timestamp}.jpg"
            cv2.imwrite(str(image_path), face_image)
            
            return str(image_path)
            
        except Exception as e:
            self.logger.error(f"خطأ في حفظ صورة الوجه غير المعروف: {e}")
            return ""

    def _save_spoofing_attempt_image(self, frame: np.ndarray) -> str:
        """حفظ صورة محاولة التلاعب"""
        try:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S_%f")[:-3]
            spoofing_dir = Path("data/spoofing_attempts")
            spoofing_dir.mkdir(parents=True, exist_ok=True)

            image_path = spoofing_dir / f"spoofing_{timestamp}.jpg"
            cv2.imwrite(str(image_path), frame)

            return str(image_path)

        except Exception as e:
            self.logger.error(f"خطأ في حفظ صورة محاولة التلاعب: {e}")
            return ""

    def _should_send_alert(self, alert_type: str) -> bool:
        """التحقق من إمكانية إرسال تنبيه"""
        current_time = datetime.now()

        # التحقق من الحد الأقصى للتنبيهات في الدقيقة
        if alert_type == 'unknown_face':
            # عد التنبيهات في الدقيقة الماضية
            one_minute_ago = current_time - timedelta(minutes=1)
            recent_alerts = [
                alert for alert in self.unknown_face_alerts
                if alert > one_minute_ago
            ]

            if len(recent_alerts) >= self.settings.security.max_unknown_alerts_per_minute:
                return False

        # التحقق من آخر تنبيه من نفس النوع
        last_alert = self.last_alert_time.get(alert_type)
        if last_alert:
            time_diff = (current_time - last_alert).seconds
            if time_diff < 10:  # لا ترسل تنبيه إذا كان آخر تنبيه قبل أقل من 10 ثوان
                return False

        return True

    def _trigger_alert(self, alert_data: Dict):
        """إرسال تنبيه"""
        try:
            alert_type = alert_data['type']
            current_time = alert_data['timestamp']

            # تحديث آخر وقت تنبيه
            self.last_alert_time[alert_type] = current_time

            # إضافة إلى قائمة التنبيهات
            if alert_type == 'unknown_face':
                self.unknown_face_alerts.append(current_time)
                # الاحتفاظ بآخر 100 تنبيه فقط
                if len(self.unknown_face_alerts) > 100:
                    self.unknown_face_alerts = self.unknown_face_alerts[-100:]

            # تسجيل التنبيه في قاعدة البيانات
            self.db.log_system_event(
                event_type="SECURITY_ALERT",
                event_data=json.dumps(alert_data, default=str),
                severity="WARNING"
            )

            # استدعاء callbacks المسجلة
            for callback in self.alert_callbacks:
                try:
                    callback(alert_data)
                except Exception as e:
                    self.logger.error(f"خطأ في استدعاء callback التنبيه: {e}")

            # تسجيل في السجل
            self.logger.log_security_alert(
                alert_type.upper(),
                f"تنبيه أمني: {alert_data}"
            )

            self.security_stats['total_alerts'] += 1

        except Exception as e:
            self.logger.error(f"خطأ في إرسال التنبيه: {e}")

    def _security_monitor(self):
        """مراقب الأمان في الخلفية"""
        while self.is_monitoring:
            try:
                # فحص حالة النظام
                self._check_system_health()

                # تنظيف الملفات القديمة
                self._cleanup_old_files()

                # فحص مساحة القرص
                self._check_disk_space()

                time.sleep(60)  # فحص كل دقيقة

            except Exception as e:
                self.logger.error(f"خطأ في مراقب الأمان: {e}")
                time.sleep(5)

    def _check_system_health(self):
        """فحص صحة النظام"""
        try:
            # فحص استخدام الموارد
            import psutil

            cpu_percent = psutil.cpu_percent(interval=1)
            memory_percent = psutil.virtual_memory().percent

            # تنبيهات الموارد
            if cpu_percent > 95:
                alert_data = {
                    'type': 'high_cpu_usage',
                    'timestamp': datetime.now(),
                    'cpu_usage': cpu_percent,
                    'severity': 'HIGH'
                }
                self._trigger_alert(alert_data)

            if memory_percent > 95:
                alert_data = {
                    'type': 'high_memory_usage',
                    'timestamp': datetime.now(),
                    'memory_usage': memory_percent,
                    'severity': 'HIGH'
                }
                self._trigger_alert(alert_data)

        except Exception as e:
            self.logger.error(f"خطأ في فحص صحة النظام: {e}")

    def _cleanup_old_files(self):
        """تنظيف الملفات القديمة"""
        try:
            current_time = datetime.now()
            cleanup_age = timedelta(days=7)  # حذف الملفات الأقدم من أسبوع

            # تنظيف صور الوجوه غير المعروفة
            unknown_faces_dir = Path("data/unknown_faces")
            if unknown_faces_dir.exists():
                for file_path in unknown_faces_dir.glob("*.jpg"):
                    file_time = datetime.fromtimestamp(file_path.stat().st_mtime)
                    if current_time - file_time > cleanup_age:
                        file_path.unlink()

            # تنظيف تسجيلات الفيديو القديمة
            recordings_dir = Path(self.settings.security.recording_path)
            if recordings_dir.exists():
                for file_path in recordings_dir.glob("*.mp4"):
                    file_time = datetime.fromtimestamp(file_path.stat().st_mtime)
                    if current_time - file_time > cleanup_age:
                        file_path.unlink()
                        self.logger.info(f"تم حذف تسجيل قديم: {file_path}")

        except Exception as e:
            self.logger.error(f"خطأ في تنظيف الملفات القديمة: {e}")

    def _check_disk_space(self):
        """فحص مساحة القرص"""
        try:
            import shutil

            # فحص مساحة القرص المتاحة
            total, used, free = shutil.disk_usage(".")
            free_percent = (free / total) * 100

            if free_percent < 10:  # أقل من 10% مساحة متاحة
                alert_data = {
                    'type': 'low_disk_space',
                    'timestamp': datetime.now(),
                    'free_space_percent': free_percent,
                    'severity': 'HIGH'
                }
                self._trigger_alert(alert_data)

        except Exception as e:
            self.logger.error(f"خطأ في فحص مساحة القرص: {e}")

    def add_alert_callback(self, callback: Callable):
        """إضافة callback للتنبيهات"""
        self.alert_callbacks.append(callback)

    def remove_alert_callback(self, callback: Callable):
        """إزالة callback للتنبيهات"""
        if callback in self.alert_callbacks:
            self.alert_callbacks.remove(callback)

    def get_security_statistics(self) -> Dict:
        """الحصول على إحصائيات الأمان"""
        session_duration = datetime.now() - self.security_stats['session_start']

        return {
            'session_duration': str(session_duration).split('.')[0],
            'total_alerts': self.security_stats['total_alerts'],
            'unknown_faces_detected': self.security_stats['unknown_faces_detected'],
            'spoofing_attempts': self.security_stats['spoofing_attempts'],
            'recordings_created': self.security_stats['recordings_created'],
            'is_recording': self.is_recording,
            'current_recording_path': str(self.current_recording_path) if self.current_recording_path else None,
            'monitoring_active': self.is_monitoring
        }

    def _update_security_stats(self, security_info: Dict):
        """تحديث إحصائيات الأمان"""
        self.security_stats['unknown_faces_detected'] += security_info['unknown_faces']

        if security_info['spoofing_detected']:
            self.security_stats['spoofing_attempts'] += 1

    def generate_security_report(self) -> Dict:
        """إنشاء تقرير أمني شامل"""
        try:
            # إحصائيات من قاعدة البيانات
            recent_events = self.db.get_system_events(hours=24)
            security_events = [e for e in recent_events if e['event_type'] in
                             ['SECURITY_ALERT', 'SPOOFING_ATTEMPT', 'VIDEO_RECORDING']]

            # تحليل التنبيهات
            alert_types = {}
            for event in security_events:
                event_type = event['event_type']
                alert_types[event_type] = alert_types.get(event_type, 0) + 1

            return {
                'report_generated': datetime.now().isoformat(),
                'session_stats': self.get_security_statistics(),
                'recent_events_count': len(security_events),
                'alert_breakdown': alert_types,
                'system_status': {
                    'monitoring_active': self.is_monitoring,
                    'recording_enabled': self.settings.security.enable_recording,
                    'spoofing_detection_enabled': self.enable_spoofing_detection
                }
            }

        except Exception as e:
            self.logger.error(f"خطأ في إنشاء التقرير الأمني: {e}")
            return {}
