{% extends "base.html" %}

{% block title %}الإعدادات - نظام التعرف على الوجوه{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h2><i class="bi bi-gear me-2"></i>إعدادات النظام</h2>
    <div>
        <button class="btn btn-success" onclick="saveAllSettings()">
            <i class="bi bi-check-circle"></i> حفظ جميع الإعدادات
        </button>
        <button class="btn btn-warning" onclick="resetToDefaults()">
            <i class="bi bi-arrow-clockwise"></i> إعادة تعيين افتراضية
        </button>
    </div>
</div>

<!-- Settings Tabs -->
<ul class="nav nav-tabs" id="settingsTabs" role="tablist">
    <li class="nav-item" role="presentation">
        <button class="nav-link active" id="camera-tab" data-bs-toggle="tab" data-bs-target="#camera-settings" type="button" role="tab">
            <i class="bi bi-camera-video me-2"></i>الكاميرا
        </button>
    </li>
    <li class="nav-item" role="presentation">
        <button class="nav-link" id="recognition-tab" data-bs-toggle="tab" data-bs-target="#recognition-settings" type="button" role="tab">
            <i class="bi bi-eye me-2"></i>التعرف
        </button>
    </li>
    <li class="nav-item" role="presentation">
        <button class="nav-link" id="ui-tab" data-bs-toggle="tab" data-bs-target="#ui-settings" type="button" role="tab">
            <i class="bi bi-display me-2"></i>الواجهة
        </button>
    </li>
    <li class="nav-item" role="presentation">
        <button class="nav-link" id="security-tab" data-bs-toggle="tab" data-bs-target="#security-settings" type="button" role="tab">
            <i class="bi bi-shield-check me-2"></i>الأمان
        </button>
    </li>
    <li class="nav-item" role="presentation">
        <button class="nav-link" id="performance-tab" data-bs-toggle="tab" data-bs-target="#performance-settings" type="button" role="tab">
            <i class="bi bi-speedometer2 me-2"></i>الأداء
        </button>
    </li>
</ul>

<div class="tab-content mt-4" id="settingsTabContent">
    <!-- Camera Settings -->
    <div class="tab-pane fade show active" id="camera-settings" role="tabpanel">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="bi bi-camera-video me-2"></i>إعدادات الكاميرا</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="camera-index" class="form-label">فهرس الكاميرا</label>
                            <select class="form-select" id="camera-index">
                                <option value="0">الكاميرا الافتراضية (0)</option>
                                <option value="1">الكاميرا الثانية (1)</option>
                                <option value="2">الكاميرا الثالثة (2)</option>
                            </select>
                            <div class="form-text">اختر الكاميرا المراد استخدامها</div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="camera-fps" class="form-label">معدل الإطارات (FPS)</label>
                            <input type="number" class="form-control" id="camera-fps" min="10" max="60" value="30">
                            <div class="form-text">عدد الإطارات في الثانية (10-60)</div>
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="resolution-width" class="form-label">عرض الدقة</label>
                            <select class="form-select" id="resolution-width">
                                <option value="640">640 (منخفض)</option>
                                <option value="800">800 (متوسط)</option>
                                <option value="1280">1280 (عالي)</option>
                                <option value="1920">1920 (عالي جداً)</option>
                            </select>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="resolution-height" class="form-label">ارتفاع الدقة</label>
                            <select class="form-select" id="resolution-height">
                                <option value="480">480 (منخفض)</option>
                                <option value="600">600 (متوسط)</option>
                                <option value="720">720 (عالي)</option>
                                <option value="1080">1080 (عالي جداً)</option>
                            </select>
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="camera-brightness" class="form-label">السطوع</label>
                            <input type="range" class="form-range" id="camera-brightness" min="0" max="1" step="0.1" value="0.5">
                            <div class="form-text">مستوى السطوع (0.0 - 1.0)</div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="camera-contrast" class="form-label">التباين</label>
                            <input type="range" class="form-range" id="camera-contrast" min="0" max="1" step="0.1" value="0.5">
                            <div class="form-text">مستوى التباين (0.0 - 1.0)</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Recognition Settings -->
    <div class="tab-pane fade" id="recognition-settings" role="tabpanel">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="bi bi-eye me-2"></i>إعدادات التعرف</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="confidence-threshold" class="form-label">عتبة الثقة</label>
                            <input type="range" class="form-range" id="confidence-threshold" min="0.3" max="0.9" step="0.05" value="0.6">
                            <div class="form-text">مستوى الثقة المطلوب للتعرف (<span id="confidence-value">0.6</span>)</div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="recognition-tolerance" class="form-label">تسامح التعرف</label>
                            <input type="range" class="form-range" id="recognition-tolerance" min="0.3" max="0.9" step="0.05" value="0.6">
                            <div class="form-text">مستوى التسامح في المقارنة (<span id="tolerance-value">0.6</span>)</div>
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="face-detection-model" class="form-label">نموذج اكتشاف الوجوه</label>
                            <select class="form-select" id="face-detection-model">
                                <option value="hog">HOG (سريع، دقة متوسطة)</option>
                                <option value="cnn">CNN (بطيء، دقة عالية)</option>
                            </select>
                            <div class="form-text">اختر النموذج المناسب لجهازك</div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="face-encoding-model" class="form-label">نموذج ترميز الوجوه</label>
                            <select class="form-select" id="face-encoding-model">
                                <option value="small">صغير (سريع)</option>
                                <option value="large">كبير (دقيق)</option>
                            </select>
                            <div class="form-text">حجم النموذج المستخدم للترميز</div>
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="max-faces-per-frame" class="form-label">أقصى عدد وجوه في الإطار</label>
                            <input type="number" class="form-control" id="max-faces-per-frame" min="1" max="20" value="10">
                            <div class="form-text">العدد الأقصى للوجوه المعالجة في إطار واحد</div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="frame-skip" class="form-label">تخطي الإطارات</label>
                            <input type="number" class="form-control" id="frame-skip" min="1" max="10" value="2">
                            <div class="form-text">معالجة كل إطار رقم (لتحسين الأداء)</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- UI Settings -->
    <div class="tab-pane fade" id="ui-settings" role="tabpanel">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="bi bi-display me-2"></i>إعدادات الواجهة</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="window-width" class="form-label">عرض النافذة</label>
                            <select class="form-select" id="window-width">
                                <option value="640">640 (صغير)</option>
                                <option value="800">800 (متوسط)</option>
                                <option value="1024">1024 (كبير)</option>
                                <option value="1280">1280 (كبير جداً)</option>
                            </select>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="window-height" class="form-label">ارتفاع النافذة</label>
                            <select class="form-select" id="window-height">
                                <option value="480">480 (صغير)</option>
                                <option value="600">600 (متوسط)</option>
                                <option value="768">768 (كبير)</option>
                                <option value="720">720 (كبير جداً)</option>
                            </select>
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-4">
                        <div class="form-check form-switch mb-3">
                            <input class="form-check-input" type="checkbox" id="show-fps" checked>
                            <label class="form-check-label" for="show-fps">عرض FPS</label>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="form-check form-switch mb-3">
                            <input class="form-check-input" type="checkbox" id="show-confidence" checked>
                            <label class="form-check-label" for="show-confidence">عرض مستوى الثقة</label>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="form-check form-switch mb-3">
                            <input class="form-check-input" type="checkbox" id="show-statistics" checked>
                            <label class="form-check-label" for="show-statistics">عرض الإحصائيات</label>
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="font-scale" class="form-label">حجم الخط</label>
                            <input type="range" class="form-range" id="font-scale" min="0.5" max="2.0" step="0.1" value="0.8">
                            <div class="form-text">حجم النص المعروض (<span id="font-scale-value">0.8</span>)</div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="box-thickness" class="form-label">سمك الإطار</label>
                            <input type="range" class="form-range" id="box-thickness" min="1" max="5" step="1" value="2">
                            <div class="form-text">سمك إطار الوجه (<span id="box-thickness-value">2</span>)</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Security Settings -->
    <div class="tab-pane fade" id="security-settings" role="tabpanel">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="bi bi-shield-check me-2"></i>إعدادات الأمان</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-check form-switch mb-3">
                            <input class="form-check-input" type="checkbox" id="enable-recording">
                            <label class="form-check-label" for="enable-recording">تفعيل تسجيل الفيديو</label>
                        </div>
                        <div class="form-check form-switch mb-3">
                            <input class="form-check-input" type="checkbox" id="enable-alerts" checked>
                            <label class="form-check-label" for="enable-alerts">تفعيل التنبيهات</label>
                        </div>
                        <div class="form-check form-switch mb-3">
                            <input class="form-check-input" type="checkbox" id="alert-unknown-faces" checked>
                            <label class="form-check-label" for="alert-unknown-faces">تنبيه للوجوه غير المعروفة</label>
                        </div>
                        <div class="form-check form-switch mb-3">
                            <input class="form-check-input" type="checkbox" id="enable-spoofing-detection">
                            <label class="form-check-label" for="enable-spoofing-detection">كشف محاولات التلاعب</label>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="recording-path" class="form-label">مسار حفظ التسجيلات</label>
                            <input type="text" class="form-control" id="recording-path" value="data/videos">
                            <div class="form-text">المجلد الذي سيتم حفظ التسجيلات فيه</div>
                        </div>
                        <div class="mb-3">
                            <label for="max-unknown-alerts" class="form-label">أقصى تنبيهات في الدقيقة</label>
                            <input type="number" class="form-control" id="max-unknown-alerts" min="1" max="20" value="5">
                            <div class="form-text">العدد الأقصى للتنبيهات للوجوه غير المعروفة في الدقيقة الواحدة</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Performance Settings -->
    <div class="tab-pane fade" id="performance-settings" role="tabpanel">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="bi bi-speedometer2 me-2"></i>إعدادات الأداء</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-check form-switch mb-3">
                            <input class="form-check-input" type="checkbox" id="enable-multithreading" checked>
                            <label class="form-check-label" for="enable-multithreading">تفعيل المعالجة متعددة الخيوط</label>
                        </div>
                        <div class="form-check form-switch mb-3">
                            <input class="form-check-input" type="checkbox" id="enable-gpu-acceleration">
                            <label class="form-check-label" for="enable-gpu-acceleration">تسريع GPU (تجريبي)</label>
                        </div>
                        <div class="form-check form-switch mb-3">
                            <input class="form-check-input" type="checkbox" id="memory-optimization" checked>
                            <label class="form-check-label" for="memory-optimization">تحسين استخدام الذاكرة</label>
                        </div>
                        <div class="form-check form-switch mb-3">
                            <input class="form-check-input" type="checkbox" id="cache-encodings" checked>
                            <label class="form-check-label" for="cache-encodings">تخزين الترميزات مؤقتاً</label>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="max-worker-threads" class="form-label">عدد خيوط المعالجة</label>
                            <input type="number" class="form-control" id="max-worker-threads" min="1" max="8" value="4">
                            <div class="form-text">عدد الخيوط المستخدمة في المعالجة المتوازية</div>
                        </div>
                        <div class="mb-3">
                            <label for="image-resize-factor" class="form-label">عامل تصغير الصورة</label>
                            <input type="range" class="form-range" id="image-resize-factor" min="0.1" max="1.0" step="0.05" value="0.25">
                            <div class="form-text">نسبة تصغير الصورة للمعالجة (<span id="resize-factor-value">0.25</span>)</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Current Settings Display -->
<div class="card mt-4">
    <div class="card-header">
        <h5 class="mb-0"><i class="bi bi-info-circle me-2"></i>الإعدادات الحالية</h5>
    </div>
    <div class="card-body">
        <pre id="current-settings-display" class="bg-light p-3 rounded" style="max-height: 300px; overflow-y: auto;">
جاري تحميل الإعدادات...
        </pre>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    let currentSettings = {};
    
    document.addEventListener('DOMContentLoaded', function() {
        loadCurrentSettings();
        setupRangeInputs();
    });
    
    async function loadCurrentSettings() {
        try {
            currentSettings = await apiCall('/api/settings');
            populateSettingsForm();
            displayCurrentSettings();
        } catch (error) {
            console.error('Failed to load settings:', error);
            showAlert('فشل في تحميل الإعدادات', 'danger');
        }
    }
    
    function populateSettingsForm() {
        // Camera settings
        if (currentSettings.camera) {
            document.getElementById('camera-index').value = currentSettings.camera.camera_index || 0;
            document.getElementById('camera-fps').value = currentSettings.camera.fps || 30;
            document.getElementById('resolution-width').value = currentSettings.camera.resolution_width || 800;
            document.getElementById('resolution-height').value = currentSettings.camera.resolution_height || 600;
            document.getElementById('camera-brightness').value = currentSettings.camera.brightness || 0.5;
            document.getElementById('camera-contrast').value = currentSettings.camera.contrast || 0.5;
        }
        
        // Recognition settings
        if (currentSettings.recognition) {
            document.getElementById('confidence-threshold').value = currentSettings.recognition.confidence_threshold || 0.6;
            document.getElementById('recognition-tolerance').value = currentSettings.recognition.recognition_tolerance || 0.6;
            document.getElementById('face-detection-model').value = currentSettings.recognition.face_detection_model || 'hog';
            document.getElementById('face-encoding-model').value = currentSettings.recognition.face_encoding_model || 'large';
            document.getElementById('max-faces-per-frame').value = currentSettings.recognition.max_faces_per_frame || 10;
            document.getElementById('frame-skip').value = currentSettings.recognition.frame_skip || 2;
        }
        
        // UI settings
        if (currentSettings.ui) {
            document.getElementById('window-width').value = currentSettings.ui.window_width || 800;
            document.getElementById('window-height').value = currentSettings.ui.window_height || 600;
            document.getElementById('show-fps').checked = currentSettings.ui.show_fps !== false;
            document.getElementById('show-confidence').checked = currentSettings.ui.show_confidence !== false;
            document.getElementById('show-statistics').checked = currentSettings.ui.show_statistics !== false;
            document.getElementById('font-scale').value = currentSettings.ui.font_scale || 0.8;
            document.getElementById('box-thickness').value = currentSettings.ui.box_thickness || 2;
        }
        
        // Security settings
        if (currentSettings.security) {
            document.getElementById('enable-recording').checked = currentSettings.security.enable_recording || false;
            document.getElementById('enable-alerts').checked = currentSettings.security.enable_alerts !== false;
            document.getElementById('alert-unknown-faces').checked = currentSettings.security.alert_unknown_faces !== false;
            document.getElementById('enable-spoofing-detection').checked = currentSettings.security.enable_face_spoofing_detection || false;
            document.getElementById('recording-path').value = currentSettings.security.recording_path || 'data/videos';
            document.getElementById('max-unknown-alerts').value = currentSettings.security.max_unknown_alerts_per_minute || 5;
        }
        
        // Performance settings
        if (currentSettings.performance) {
            document.getElementById('enable-multithreading').checked = currentSettings.performance.enable_multithreading !== false;
            document.getElementById('enable-gpu-acceleration').checked = currentSettings.performance.enable_gpu_acceleration || false;
            document.getElementById('memory-optimization').checked = currentSettings.performance.memory_optimization !== false;
            document.getElementById('cache-encodings').checked = currentSettings.performance.cache_encodings !== false;
            document.getElementById('max-worker-threads').value = currentSettings.performance.max_worker_threads || 4;
            document.getElementById('image-resize-factor').value = currentSettings.performance.image_resize_factor || 0.25;
        }
        
        updateRangeDisplays();
    }
    
    function setupRangeInputs() {
        // إعداد عرض القيم للمدخلات المنزلقة
        const ranges = [
            { id: 'confidence-threshold', display: 'confidence-value' },
            { id: 'recognition-tolerance', display: 'tolerance-value' },
            { id: 'font-scale', display: 'font-scale-value' },
            { id: 'box-thickness', display: 'box-thickness-value' },
            { id: 'image-resize-factor', display: 'resize-factor-value' }
        ];
        
        ranges.forEach(range => {
            const input = document.getElementById(range.id);
            const display = document.getElementById(range.display);
            
            if (input && display) {
                input.addEventListener('input', function() {
                    display.textContent = this.value;
                });
            }
        });
    }
    
    function updateRangeDisplays() {
        document.getElementById('confidence-value').textContent = document.getElementById('confidence-threshold').value;
        document.getElementById('tolerance-value').textContent = document.getElementById('recognition-tolerance').value;
        document.getElementById('font-scale-value').textContent = document.getElementById('font-scale').value;
        document.getElementById('box-thickness-value').textContent = document.getElementById('box-thickness').value;
        document.getElementById('resize-factor-value').textContent = document.getElementById('image-resize-factor').value;
    }
    
    function displayCurrentSettings() {
        const display = document.getElementById('current-settings-display');
        display.textContent = JSON.stringify(currentSettings, null, 2);
    }
    
    async function saveAllSettings() {
        try {
            const newSettings = {
                camera: {
                    camera_index: parseInt(document.getElementById('camera-index').value),
                    fps: parseInt(document.getElementById('camera-fps').value),
                    resolution_width: parseInt(document.getElementById('resolution-width').value),
                    resolution_height: parseInt(document.getElementById('resolution-height').value),
                    brightness: parseFloat(document.getElementById('camera-brightness').value),
                    contrast: parseFloat(document.getElementById('camera-contrast').value)
                },
                recognition: {
                    confidence_threshold: parseFloat(document.getElementById('confidence-threshold').value),
                    recognition_tolerance: parseFloat(document.getElementById('recognition-tolerance').value),
                    face_detection_model: document.getElementById('face-detection-model').value,
                    face_encoding_model: document.getElementById('face-encoding-model').value,
                    max_faces_per_frame: parseInt(document.getElementById('max-faces-per-frame').value),
                    frame_skip: parseInt(document.getElementById('frame-skip').value)
                },
                ui: {
                    window_width: parseInt(document.getElementById('window-width').value),
                    window_height: parseInt(document.getElementById('window-height').value),
                    show_fps: document.getElementById('show-fps').checked,
                    show_confidence: document.getElementById('show-confidence').checked,
                    show_statistics: document.getElementById('show-statistics').checked,
                    font_scale: parseFloat(document.getElementById('font-scale').value),
                    font_thickness: 2,
                    box_thickness: parseInt(document.getElementById('box-thickness').value),
                    language: "ar"
                },
                security: {
                    enable_recording: document.getElementById('enable-recording').checked,
                    enable_alerts: document.getElementById('enable-alerts').checked,
                    alert_unknown_faces: document.getElementById('alert-unknown-faces').checked,
                    enable_face_spoofing_detection: document.getElementById('enable-spoofing-detection').checked,
                    recording_path: document.getElementById('recording-path').value,
                    max_unknown_alerts_per_minute: parseInt(document.getElementById('max-unknown-alerts').value)
                },
                performance: {
                    enable_multithreading: document.getElementById('enable-multithreading').checked,
                    enable_gpu_acceleration: document.getElementById('enable-gpu-acceleration').checked,
                    memory_optimization: document.getElementById('memory-optimization').checked,
                    cache_encodings: document.getElementById('cache-encodings').checked,
                    max_worker_threads: parseInt(document.getElementById('max-worker-threads').value),
                    image_resize_factor: parseFloat(document.getElementById('image-resize-factor').value)
                }
            };
            
            const result = await apiCall('/api/settings', {
                method: 'POST',
                body: JSON.stringify(newSettings)
            });
            
            currentSettings = newSettings;
            displayCurrentSettings();
            showAlert('تم حفظ الإعدادات بنجاح! قد تحتاج لإعادة تشغيل النظام لتطبيق بعض التغييرات.', 'success');
            
        } catch (error) {
            console.error('Failed to save settings:', error);
            showAlert('فشل في حفظ الإعدادات', 'danger');
        }
    }
    
    function resetToDefaults() {
        if (confirm('هل أنت متأكد من إعادة تعيين جميع الإعدادات للقيم الافتراضية؟')) {
            // إعادة تعيين القيم الافتراضية
            document.getElementById('camera-index').value = 0;
            document.getElementById('camera-fps').value = 30;
            document.getElementById('resolution-width').value = 800;
            document.getElementById('resolution-height').value = 600;
            document.getElementById('camera-brightness').value = 0.5;
            document.getElementById('camera-contrast').value = 0.5;
            
            document.getElementById('confidence-threshold').value = 0.6;
            document.getElementById('recognition-tolerance').value = 0.6;
            document.getElementById('face-detection-model').value = 'hog';
            document.getElementById('face-encoding-model').value = 'large';
            document.getElementById('max-faces-per-frame').value = 10;
            document.getElementById('frame-skip').value = 2;
            
            document.getElementById('window-width').value = 800;
            document.getElementById('window-height').value = 600;
            document.getElementById('show-fps').checked = true;
            document.getElementById('show-confidence').checked = true;
            document.getElementById('show-statistics').checked = true;
            document.getElementById('font-scale').value = 0.8;
            document.getElementById('box-thickness').value = 2;
            
            document.getElementById('enable-recording').checked = false;
            document.getElementById('enable-alerts').checked = true;
            document.getElementById('alert-unknown-faces').checked = true;
            document.getElementById('enable-spoofing-detection').checked = false;
            document.getElementById('recording-path').value = 'data/videos';
            document.getElementById('max-unknown-alerts').value = 5;
            
            document.getElementById('enable-multithreading').checked = true;
            document.getElementById('enable-gpu-acceleration').checked = false;
            document.getElementById('memory-optimization').checked = true;
            document.getElementById('cache-encodings').checked = true;
            document.getElementById('max-worker-threads').value = 4;
            document.getElementById('image-resize-factor').value = 0.25;
            
            updateRangeDisplays();
            showAlert('تم إعادة تعيين الإعدادات للقيم الافتراضية', 'info');
        }
    }
</script>
{% endblock %}
