{% extends "base.html" %}

{% block title %}التعرف على الوجوه - نظام التعرف على الوجوه{% endblock %}

{% block content %}
<!-- Header -->
<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h2><i class="bi bi-camera me-2"></i>التعرف على الوجوه</h2>
        <p class="text-muted mb-0">نظام التعرف المباشر على الوجوه باستخدام الكاميرا</p>
    </div>
    <div>
        <button id="start-recognition-btn" class="btn btn-success me-2">
            <i class="bi bi-play-fill me-1"></i>بدء التعرف
        </button>
        <button id="stop-recognition-btn" class="btn btn-danger" disabled>
            <i class="bi bi-stop-fill me-1"></i>إيقاف التعرف
        </button>
    </div>
</div>

<!-- Status Cards -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card bg-primary text-white">
            <div class="card-body text-center">
                <h4 id="fps-counter">0</h4>
                <p class="mb-0">إطار/ثانية</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-info text-white">
            <div class="card-body text-center">
                <h4 id="faces-detected">0</h4>
                <p class="mb-0">وجوه مكتشفة</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-success text-white">
            <div class="card-body text-center">
                <h4 id="faces-recognized">0</h4>
                <p class="mb-0">وجوه معروفة</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-warning text-white">
            <div class="card-body text-center">
                <h4 id="unknown-faces">0</h4>
                <p class="mb-0">وجوه غير معروفة</p>
            </div>
        </div>
    </div>
</div>

<!-- Main Content -->
<div class="row">
    <!-- Camera Feed -->
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="bi bi-camera-video me-2"></i>تغذية الكاميرا المباشرة
                    <span id="camera-status" class="badge bg-secondary ms-2">غير متصلة</span>
                </h5>
            </div>
            <div class="card-body text-center">
                <div id="camera-container" style="position: relative; display: inline-block;">
                    <video id="camera-feed" width="640" height="480" style="border: 2px solid #ddd; border-radius: 8px; background: #000;" autoplay muted></video>
                    <canvas id="detection-overlay" width="640" height="480" style="position: absolute; top: 0; left: 0; pointer-events: none;"></canvas>
                </div>
                <div id="camera-error" class="alert alert-warning mt-3" style="display: none;">
                    <i class="bi bi-exclamation-triangle me-2"></i>
                    <span id="error-message">لا يمكن الوصول إلى الكاميرا</span>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Recognition Results -->
    <div class="col-md-4">
        <!-- Current Recognition -->
        <div class="card mb-3">
            <div class="card-header">
                <h6 class="mb-0"><i class="bi bi-person-check me-2"></i>التعرف الحالي</h6>
            </div>
            <div class="card-body">
                <div id="current-recognition" class="text-center">
                    <div class="text-muted">
                        <i class="bi bi-camera display-4"></i>
                        <p class="mt-2">لا يوجد تعرف حالياً</p>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Recent Recognitions -->
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0"><i class="bi bi-clock-history me-2"></i>التعرف الأخير</h6>
            </div>
            <div class="card-body" style="max-height: 400px; overflow-y: auto;">
                <div id="recent-recognitions">
                    <div class="text-muted text-center">
                        <i class="bi bi-clock-history display-6"></i>
                        <p class="mt-2">لا يوجد تعرف سابق</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Settings Panel -->
<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0"><i class="bi bi-gear me-2"></i>إعدادات التعرف</h6>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3">
                        <label for="confidence-threshold" class="form-label">حد الثقة</label>
                        <input type="range" class="form-range" id="confidence-threshold" min="0.3" max="0.9" step="0.1" value="0.6">
                        <small class="text-muted">القيمة: <span id="confidence-value">0.6</span></small>
                    </div>
                    <div class="col-md-3">
                        <label for="detection-interval" class="form-label">فترة التحديث (مللي ثانية)</label>
                        <input type="range" class="form-range" id="detection-interval" min="100" max="1000" step="100" value="500">
                        <small class="text-muted">القيمة: <span id="interval-value">500</span> مللي ثانية</small>
                    </div>
                    <div class="col-md-3">
                        <div class="form-check form-switch">
                            <input class="form-check-input" type="checkbox" id="save-unknown" checked>
                            <label class="form-check-label" for="save-unknown">
                                حفظ الوجوه غير المعروفة
                            </label>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="form-check form-switch">
                            <input class="form-check-input" type="checkbox" id="show-confidence" checked>
                            <label class="form-check-label" for="show-confidence">
                                عرض نسبة الثقة
                            </label>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
let recognition = {
    isRunning: false,
    stream: null,
    video: null,
    canvas: null,
    ctx: null,
    detectionInterval: null,
    stats: {
        fps: 0,
        facesDetected: 0,
        facesRecognized: 0,
        unknownFaces: 0
    }
};

// تهيئة النظام
document.addEventListener('DOMContentLoaded', function() {
    recognition.video = document.getElementById('camera-feed');
    recognition.canvas = document.getElementById('detection-overlay');
    recognition.ctx = recognition.canvas.getContext('2d');
    
    setupEventListeners();
    updateUI();
    
    console.log('🎥 تم تهيئة نظام التعرف على الوجوه');
});

function setupEventListeners() {
    // أزرار التحكم
    document.getElementById('start-recognition-btn').addEventListener('click', startRecognition);
    document.getElementById('stop-recognition-btn').addEventListener('click', stopRecognition);
    
    // إعدادات التعرف
    document.getElementById('confidence-threshold').addEventListener('input', function() {
        document.getElementById('confidence-value').textContent = this.value;
    });
    
    document.getElementById('detection-interval').addEventListener('input', function() {
        document.getElementById('interval-value').textContent = this.value + ' مللي ثانية';
    });
}

async function startRecognition() {
    try {
        console.log('🎬 بدء التعرف على الوجوه...');
        
        // طلب الوصول إلى الكاميرا
        recognition.stream = await navigator.mediaDevices.getUserMedia({ 
            video: { width: 640, height: 480 } 
        });
        
        recognition.video.srcObject = recognition.stream;
        recognition.isRunning = true;
        
        // تحديث واجهة المستخدم
        document.getElementById('start-recognition-btn').disabled = true;
        document.getElementById('stop-recognition-btn').disabled = false;
        document.getElementById('camera-status').textContent = 'متصلة';
        document.getElementById('camera-status').className = 'badge bg-success ms-2';
        document.getElementById('camera-error').style.display = 'none';
        
        // بدء عملية التعرف
        const interval = parseInt(document.getElementById('detection-interval').value);
        recognition.detectionInterval = setInterval(processFrame, interval);
        
        console.log('✅ تم بدء التعرف بنجاح');
        showAlert('تم بدء التعرف على الوجوه', 'success');
        
    } catch (error) {
        console.error('❌ خطأ في بدء التعرف:', error);
        showCameraError('لا يمكن الوصول إلى الكاميرا: ' + error.message);
    }
}

function stopRecognition() {
    console.log('⏹️ إيقاف التعرف على الوجوه...');
    
    recognition.isRunning = false;
    
    // إيقاف الكاميرا
    if (recognition.stream) {
        recognition.stream.getTracks().forEach(track => track.stop());
        recognition.stream = null;
    }
    
    // إيقاف التعرف
    if (recognition.detectionInterval) {
        clearInterval(recognition.detectionInterval);
        recognition.detectionInterval = null;
    }
    
    // تنظيف الفيديو والكانفاس
    recognition.video.srcObject = null;
    recognition.ctx.clearRect(0, 0, recognition.canvas.width, recognition.canvas.height);
    
    // تحديث واجهة المستخدم
    document.getElementById('start-recognition-btn').disabled = false;
    document.getElementById('stop-recognition-btn').disabled = true;
    document.getElementById('camera-status').textContent = 'غير متصلة';
    document.getElementById('camera-status').className = 'badge bg-secondary ms-2';
    
    // إعادة تعيين الإحصائيات
    recognition.stats = { fps: 0, facesDetected: 0, facesRecognized: 0, unknownFaces: 0 };
    updateUI();
    
    console.log('✅ تم إيقاف التعرف');
    showAlert('تم إيقاف التعرف على الوجوه', 'info');
}

async function processFrame() {
    if (!recognition.isRunning || !recognition.video.videoWidth) return;
    
    try {
        // التقاط الإطار الحالي
        const canvas = document.createElement('canvas');
        canvas.width = recognition.video.videoWidth;
        canvas.height = recognition.video.videoHeight;
        const ctx = canvas.getContext('2d');
        ctx.drawImage(recognition.video, 0, 0);
        
        // تحويل إلى base64
        const imageData = canvas.toDataURL('image/jpeg', 0.8);
        
        // إرسال للخادم للتعرف
        const response = await fetch('/api/recognition/process', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                image: imageData,
                confidence_threshold: parseFloat(document.getElementById('confidence-threshold').value),
                save_unknown: document.getElementById('save-unknown').checked
            })
        });
        
        const result = await response.json();
        
        if (result.success) {
            // تحديث الإحصائيات
            recognition.stats.facesDetected = result.faces_detected || 0;
            recognition.stats.facesRecognized = result.faces_recognized || 0;
            recognition.stats.unknownFaces = result.unknown_faces || 0;
            
            // رسم النتائج على الكانفاس
            drawDetections(result.detections || []);
            
            // تحديث التعرف الحالي
            updateCurrentRecognition(result.detections || []);
            
            // إضافة إلى التعرف الأخير
            if (result.detections && result.detections.length > 0) {
                addToRecentRecognitions(result.detections);
            }
        }
        
        updateUI();
        
    } catch (error) {
        console.error('خطأ في معالجة الإطار:', error);
    }
}

function drawDetections(detections) {
    // تنظيف الكانفاس
    recognition.ctx.clearRect(0, 0, recognition.canvas.width, recognition.canvas.height);
    
    detections.forEach(detection => {
        const { x, y, width, height, name, confidence } = detection;
        
        // رسم المربع
        recognition.ctx.strokeStyle = name === 'Unknown' ? '#dc3545' : '#28a745';
        recognition.ctx.lineWidth = 3;
        recognition.ctx.strokeRect(x, y, width, height);
        
        // رسم النص
        recognition.ctx.fillStyle = name === 'Unknown' ? '#dc3545' : '#28a745';
        recognition.ctx.font = '16px Arial';
        
        const text = document.getElementById('show-confidence').checked ? 
            `${name} (${(confidence * 100).toFixed(1)}%)` : name;
        
        recognition.ctx.fillText(text, x, y - 10);
    });
}

function updateCurrentRecognition(detections) {
    const container = document.getElementById('current-recognition');
    
    if (detections.length === 0) {
        container.innerHTML = `
            <div class="text-muted">
                <i class="bi bi-camera display-4"></i>
                <p class="mt-2">لا يوجد تعرف حالياً</p>
            </div>
        `;
        return;
    }
    
    const html = detections.map(detection => `
        <div class="mb-2 p-2 border rounded ${detection.name === 'Unknown' ? 'border-danger' : 'border-success'}">
            <strong>${detection.name}</strong>
            <br>
            <small class="text-muted">الثقة: ${(detection.confidence * 100).toFixed(1)}%</small>
        </div>
    `).join('');
    
    container.innerHTML = html;
}

function addToRecentRecognitions(detections) {
    const container = document.getElementById('recent-recognitions');
    const timestamp = new Date().toLocaleTimeString('ar-SA');
    
    detections.forEach(detection => {
        const item = document.createElement('div');
        item.className = `mb-2 p-2 border-start border-3 ${detection.name === 'Unknown' ? 'border-danger' : 'border-success'}`;
        item.innerHTML = `
            <div class="d-flex justify-content-between">
                <strong>${detection.name}</strong>
                <small class="text-muted">${timestamp}</small>
            </div>
            <small class="text-muted">الثقة: ${(detection.confidence * 100).toFixed(1)}%</small>
        `;
        
        container.insertBefore(item, container.firstChild);
        
        // الاحتفاظ بآخر 10 نتائج فقط
        while (container.children.length > 10) {
            container.removeChild(container.lastChild);
        }
    });
    
    // إزالة رسالة "لا يوجد تعرف سابق"
    const emptyMessage = container.querySelector('.text-muted.text-center');
    if (emptyMessage) {
        emptyMessage.remove();
    }
}

function updateUI() {
    document.getElementById('fps-counter').textContent = recognition.stats.fps;
    document.getElementById('faces-detected').textContent = recognition.stats.facesDetected;
    document.getElementById('faces-recognized').textContent = recognition.stats.facesRecognized;
    document.getElementById('unknown-faces').textContent = recognition.stats.unknownFaces;
}

function showCameraError(message) {
    document.getElementById('error-message').textContent = message;
    document.getElementById('camera-error').style.display = 'block';
    document.getElementById('camera-status').textContent = 'خطأ';
    document.getElementById('camera-status').className = 'badge bg-danger ms-2';
}

// تنظيف الموارد عند مغادرة الصفحة
window.addEventListener('beforeunload', function() {
    if (recognition.isRunning) {
        stopRecognition();
    }
});

console.log('🎥 تم تحميل صفحة التعرف على الوجوه');
</script>
{% endblock %}
