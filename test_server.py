#!/usr/bin/env python3
"""
اختبار اتصال الخادم
Test Server Connection
"""

import requests
import time
import sys

def test_server():
    """اختبار اتصال الخادم"""
    
    urls = [
        'http://localhost:5000',
        'http://127.0.0.1:5000',
        'http://************:5000'
    ]
    
    print("🔍 اختبار اتصال الخادم...")
    print("=" * 50)
    
    for url in urls:
        try:
            print(f"🔄 اختبار: {url}")
            response = requests.get(url, timeout=10)
            
            if response.status_code == 200:
                print(f"✅ نجح الاتصال! Status: {response.status_code}")
                print(f"🌐 الموقع متاح على: {url}")
                return url
            else:
                print(f"⚠️ استجابة غير متوقعة: {response.status_code}")
                
        except requests.exceptions.ConnectionError:
            print(f"❌ فشل الاتصال: الخادم غير متاح")
        except requests.exceptions.Timeout:
            print(f"⏱️ انتهت مهلة الاتصال")
        except Exception as e:
            print(f"❌ خطأ: {e}")
        
        print()
    
    print("❌ فشل في الاتصال بجميع العناوين")
    return None

def check_server_status():
    """فحص حالة الخادم"""
    
    print("📊 فحص حالة الخادم...")
    print("=" * 50)
    
    working_url = test_server()
    
    if working_url:
        print("🎉 الخادم يعمل بشكل مثالي!")
        print("=" * 50)
        print("🔑 بيانات تسجيل الدخول:")
        print("   👤 رقم الهوية: 99151413104")
        print("   👤 اسم المستخدم: admin")
        print("   🔑 كلمة المرور: admin")
        print("=" * 50)
        print("📱 الصفحات المتاحة:")
        print(f"   🏠 الصفحة الرئيسية: {working_url}")
        print(f"   🔐 تسجيل الدخول: {working_url}/login")
        print(f"   📊 لوحة التحكم: {working_url}/admin")
        print(f"   👥 إدارة الأشخاص: {working_url}/persons")
        print(f"   🎥 التعرف على الوجوه: {working_url}/recognition")
        print(f"   🔧 تشخيص الكاميرا: {working_url}/camera-test")
        print("=" * 50)
        print("🎯 للوصول إلى النظام:")
        print("   1. افتح المتصفح")
        print(f"   2. اذهب إلى: {working_url}")
        print("   3. سجل الدخول بالبيانات أعلاه")
        print("=" * 50)
        
        return True
    else:
        print("❌ الخادم لا يعمل!")
        print("🔧 حلول مقترحة:")
        print("   1. تأكد من تشغيل: python web_app.py")
        print("   2. تحقق من عدم استخدام المنفذ 5000")
        print("   3. تحقق من إعدادات الجدار الناري")
        print("   4. جرب إعادة تشغيل الخادم")
        
        return False

if __name__ == "__main__":
    try:
        success = check_server_status()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n⏹️ تم إيقاف الاختبار")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ خطأ غير متوقع: {e}")
        sys.exit(1)
