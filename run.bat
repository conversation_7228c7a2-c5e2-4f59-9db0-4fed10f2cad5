@echo off
title نظام التعرف على الوجوه الاحترافي
color 0A

echo.
echo ========================================
echo    نظام التعرف على الوجوه الاحترافي
echo ========================================
echo.
echo اختر طريقة التشغيل:
echo.
echo [1] التطبيق المكتبي (الواجهة التقليدية)
echo [2] واجهة الويب (موصى بها)
echo [3] الخروج
echo.
set /p choice="اختر رقم (1-3): "

if "%choice%"=="1" goto desktop
if "%choice%"=="2" goto web
if "%choice%"=="3" goto exit
goto invalid

:desktop
echo.
echo تشغيل التطبيق المكتبي...
echo اضغط Q أو ESC للخروج
echo.
python main.py
goto end

:web
echo.
echo تشغيل واجهة الويب...
echo افتح المتصفح على: http://localhost:5000
echo اضغط Ctrl+C لإيقاف الخادم
echo.
python web_app.py
goto end

:invalid
echo.
echo خيار غير صحيح! يرجى اختيار رقم من 1 إلى 3
echo.
pause
goto start

:exit
echo.
echo شكراً لاستخدام نظام التعرف على الوجوه الاحترافي!
timeout /t 2 >nul
exit

:end
echo.
pause
