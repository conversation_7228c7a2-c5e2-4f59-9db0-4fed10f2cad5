#!/usr/bin/env python3
"""
تطبيق الويب لنظام التعرف على الوجوه
Web Application for Face Recognition System
"""

import sys
import os
import threading
import time

# إضافة مسار المشروع إلى Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from src.config.settings import Settings
from src.database.database_manager import DatabaseManager
from src.core.face_recognition_system import FaceRecognitionSystem
from src.web.web_server import WebServer
from src.utils.logger import setup_logger

def main():
    """الدالة الرئيسية لتشغيل تطبيق الويب"""
    try:
        # إعداد نظام التسجيل
        logger = setup_logger()
        logger.info("بدء تشغيل تطبيق الويب لنظام التعرف على الوجوه")
        
        # تحميل الإعدادات
        settings = Settings()
        
        # إنشاء قاعدة البيانات
        db = DatabaseManager(settings.database.database_path)
        
        # إنشاء نظام التعرف على الوجوه (اختياري)
        face_system = None
        try:
            face_system = FaceRecognitionSystem(settings)
            logger.info("تم تهيئة نظام التعرف على الوجوه")
        except Exception as e:
            logger.warning(f"لم يتم تهيئة نظام التعرف على الوجوه: {e}")
        
        # إنشاء خادم الويب
        web_server = WebServer(settings, db, face_system)
        
        # تشغيل الخادم
        logger.info("بدء تشغيل خادم الويب على http://localhost:5000")
        web_server.start_server(host='0.0.0.0', port=5000, debug=False)
        
    except KeyboardInterrupt:
        logger.info("تم إيقاف التطبيق بواسطة المستخدم")
    except Exception as e:
        logger.error(f"خطأ في تشغيل التطبيق: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
