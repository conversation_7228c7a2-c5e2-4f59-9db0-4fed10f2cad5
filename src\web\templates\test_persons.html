<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار قائمة الأشخاص</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-4">
        <h2>اختبار قائمة الأشخاص</h2>
        
        <button class="btn btn-primary" onclick="testLoadPersons()">تحميل الأشخاص</button>
        <button class="btn btn-secondary" onclick="clearResults()">مسح النتائج</button>
        
        <div id="results" class="mt-4"></div>
        
        <div class="mt-4">
            <h4>جدول الأشخاص</h4>
            <table class="table table-bordered">
                <thead>
                    <tr>
                        <th>ID</th>
                        <th>الاسم</th>
                        <th>الاسم الكامل</th>
                        <th>الإيميل</th>
                        <th>الهاتف</th>
                    </tr>
                </thead>
                <tbody id="persons-table">
                    <tr>
                        <td colspan="5" class="text-center">اضغط "تحميل الأشخاص" لعرض البيانات</td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>

    <script>
        function log(message) {
            console.log(message);
            const results = document.getElementById('results');
            results.innerHTML += '<div class="alert alert-info">' + message + '</div>';
        }

        function logError(message) {
            console.error(message);
            const results = document.getElementById('results');
            results.innerHTML += '<div class="alert alert-danger">' + message + '</div>';
        }

        function clearResults() {
            document.getElementById('results').innerHTML = '';
        }

        async function testLoadPersons() {
            log('بدء اختبار تحميل الأشخاص...');
            
            try {
                log('إرسال طلب إلى /api/persons...');
                
                const response = await fetch('/api/persons', {
                    method: 'GET',
                    headers: {
                        'Accept': 'application/json',
                        'Content-Type': 'application/json'
                    }
                });
                
                log(`كود الاستجابة: ${response.status} ${response.statusText}`);
                
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                
                const responseText = await response.text();
                log(`طول الاستجابة: ${responseText.length} حرف`);
                log(`أول 200 حرف: ${responseText.substring(0, 200)}...`);
                
                let persons;
                try {
                    persons = JSON.parse(responseText);
                    log(`تم تحليل JSON بنجاح. نوع البيانات: ${typeof persons}`);
                    log(`عدد الأشخاص: ${Array.isArray(persons) ? persons.length : 'ليس مصفوفة'}`);
                } catch (parseError) {
                    logError(`خطأ في تحليل JSON: ${parseError.message}`);
                    return;
                }
                
                if (!Array.isArray(persons)) {
                    logError('البيانات المستلمة ليست مصفوفة');
                    return;
                }
                
                // عرض البيانات في الجدول
                const tbody = document.getElementById('persons-table');
                if (persons.length === 0) {
                    tbody.innerHTML = '<tr><td colspan="5" class="text-center">لا توجد أشخاص</td></tr>';
                } else {
                    tbody.innerHTML = persons.map(person => `
                        <tr>
                            <td>${person.id}</td>
                            <td>${person.name || 'غير محدد'}</td>
                            <td>${person.full_name || 'غير محدد'}</td>
                            <td>${person.email || 'غير محدد'}</td>
                            <td>${person.phone || 'غير محدد'}</td>
                        </tr>
                    `).join('');
                }
                
                log(`✅ تم تحميل ${persons.length} شخص بنجاح`);
                
            } catch (error) {
                logError(`خطأ في تحميل الأشخاص: ${error.message}`);
            }
        }

        // اختبار تلقائي عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            log('تم تحميل الصفحة، بدء الاختبار التلقائي...');
            setTimeout(testLoadPersons, 1000); // انتظار ثانية واحدة
        });
    </script>
</body>
</html>
