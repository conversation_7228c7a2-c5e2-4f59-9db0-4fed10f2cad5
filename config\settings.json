{"camera": {"camera_index": 0, "resolution_width": 1280, "resolution_height": 720, "fps": 30, "brightness": 0.5, "contrast": 0.5}, "recognition": {"confidence_threshold": 0.6, "face_detection_model": "hog", "face_encoding_model": "large", "max_faces_per_frame": 10, "recognition_tolerance": 0.6, "frame_skip": 2}, "ui": {"window_width": 1024, "window_height": 768, "show_fps": true, "show_confidence": true, "show_statistics": true, "font_scale": 0.8, "font_thickness": 2, "box_thickness": 2, "language": "ar"}, "security": {"enable_recording": false, "recording_path": "data/videos", "enable_alerts": false, "alert_unknown_faces": true, "max_unknown_alerts_per_minute": 5, "enable_face_spoofing_detection": false}, "database": {"database_path": "data/face_recognition.db", "backup_interval_hours": 24, "max_log_entries": 10000, "enable_statistics": true}, "performance": {"enable_multithreading": true, "max_worker_threads": 4, "image_resize_factor": 0.25, "enable_gpu_acceleration": false, "memory_optimization": true, "cache_encodings": true}}