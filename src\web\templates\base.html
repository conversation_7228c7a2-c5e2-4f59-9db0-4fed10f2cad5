<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="Cache-Control" content="no-cache, no-store, must-revalidate">
    <meta http-equiv="Pragma" content="no-cache">
    <meta http-equiv="Expires" content="0">
    <title>{% block title %}نظام التعرف على الوجوه الاحترافي{% endblock %}</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <!-- Socket.IO -->
    <script src="https://cdn.socket.io/4.7.2/socket.io.min.js"></script>
    <!-- Bootstrap JavaScript -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f8f9fa;
        }
        
        .navbar-brand {
            font-weight: bold;
            color: #2c3e50 !important;
        }
        
        .sidebar {
            min-height: 100vh;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            box-shadow: 2px 0 10px rgba(0, 0, 0, 0.1);
            position: relative;
            overflow: hidden;
        }

        .sidebar::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="rgba(255,255,255,0.05)"/><circle cx="75" cy="75" r="1" fill="rgba(255,255,255,0.05)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
            pointer-events: none;
        }
        
        .sidebar .nav-link {
            color: rgba(255, 255, 255, 0.9);
            padding: 15px 20px;
            margin: 8px 15px;
            border-radius: 12px;
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
            overflow: hidden;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.1);
            font-weight: 500;
            letter-spacing: 0.5px;
        }

        .sidebar .nav-link::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            transition: left 0.5s;
        }
        
        .sidebar .nav-link:hover {
            color: white;
            background: rgba(255, 255, 255, 0.25);
            transform: translateX(-8px) scale(1.02);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
            border-color: rgba(255, 255, 255, 0.3);
        }

        .sidebar .nav-link:hover::before {
            left: 100%;
        }

        .sidebar .nav-link.active {
            color: white;
            background: rgba(255, 255, 255, 0.3);
            transform: translateX(-5px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
            border-color: rgba(255, 255, 255, 0.4);
            font-weight: 600;
        }

        .sidebar .nav-link.active::after {
            content: '';
            position: absolute;
            right: 0;
            top: 50%;
            transform: translateY(-50%);
            width: 4px;
            height: 60%;
            background: white;
            border-radius: 2px 0 0 2px;
        }

        .sidebar .nav-link i {
            font-size: 1.2em;
            width: 24px;
            text-align: center;
            margin-right: 12px;
            transition: all 0.3s ease;
            filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.2));
        }

        .sidebar .nav-link:hover i {
            transform: scale(1.1) rotate(5deg);
            filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.3));
        }

        .sidebar .nav-link.active i {
            transform: scale(1.05);
            filter: drop-shadow(0 3px 6px rgba(0, 0, 0, 0.25));
        }

        .sidebar .nav-link[href="/"] {
            background: linear-gradient(135deg, rgba(0, 123, 255, 0.2), rgba(0, 123, 255, 0.1));
            border-left: 4px solid #007bff;
            box-shadow: inset 0 0 20px rgba(0, 123, 255, 0.1);
        }

        .sidebar .nav-link[href="/admin"] {
            background: linear-gradient(135deg, rgba(220, 53, 69, 0.2), rgba(220, 53, 69, 0.1));
            border-left: 4px solid #dc3545;
            box-shadow: inset 0 0 20px rgba(220, 53, 69, 0.1);
            position: relative;
        }

        .sidebar .nav-link[href="/admin"]::before {
            content: '👑';
            position: absolute;
            right: 15px;
            top: 50%;
            transform: translateY(-50%);
            font-size: 0.8em;
            opacity: 0.7;
            animation: crown-glow 2s infinite alternate;
        }

        .sidebar .nav-link[href="/admin"]:hover {
            background: linear-gradient(135deg, rgba(220, 53, 69, 0.4), rgba(220, 53, 69, 0.2));
            box-shadow: 0 0 20px rgba(220, 53, 69, 0.3), inset 0 0 20px rgba(220, 53, 69, 0.2);
            border-left-color: #ff6b7a;
        }

        .sidebar .nav-link[href="/admin"]:hover::before {
            content: '👑';
            opacity: 1;
            transform: translateY(-50%) scale(1.2);
        }

        @keyframes crown-glow {
            0% { opacity: 0.7; transform: translateY(-50%) scale(1); }
            100% { opacity: 1; transform: translateY(-50%) scale(1.1); }
        }

        .admin-icon {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            width: 24px;
            height: 24px;
            position: relative;
        }

        .admin-icon svg {
            transition: all 0.3s ease;
            filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.3));
        }

        .admin-panel-link:hover .admin-icon svg {
            transform: scale(1.1) rotate(5deg);
            filter: drop-shadow(0 4px 8px rgba(220, 53, 69, 0.5));
        }

        .admin-panel-link.active .admin-icon svg {
            transform: scale(1.05);
            filter: drop-shadow(0 3px 6px rgba(220, 53, 69, 0.4));
        }

        .admin-panel-link {
            position: relative;
            overflow: visible;
        }

        .admin-panel-link::after {
            content: 'ADMIN';
            position: absolute;
            top: -8px;
            right: 10px;
            background: linear-gradient(45deg, #dc3545, #ff6b7a);
            color: white;
            font-size: 0.6rem;
            font-weight: bold;
            padding: 2px 6px;
            border-radius: 8px;
            opacity: 0.8;
            letter-spacing: 0.5px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
            animation: admin-badge-pulse 3s infinite;
        }

        @keyframes admin-badge-pulse {
            0%, 100% { opacity: 0.8; transform: scale(1); }
            50% { opacity: 1; transform: scale(1.05); }
        }

        /* تأثير خاص للوحة التحكم الإدارية عند التحميل */
        .admin-panel-link {
            animation: admin-entrance 1s ease-out 0.5s both;
        }

        @keyframes admin-entrance {
            0% {
                opacity: 0;
                transform: translateX(-30px) scale(0.8);
                filter: blur(5px);
            }
            50% {
                opacity: 0.7;
                transform: translateX(-10px) scale(0.95);
                filter: blur(2px);
            }
            100% {
                opacity: 1;
                transform: translateX(0) scale(1);
                filter: blur(0);
            }
        }

        /* تأثير خاص للأيقونة عند hover */
        .admin-panel-link:hover {
            background: linear-gradient(135deg, rgba(220, 53, 69, 0.4), rgba(220, 53, 69, 0.2));
            box-shadow: 0 0 25px rgba(220, 53, 69, 0.4), inset 0 0 20px rgba(220, 53, 69, 0.2);
            border-left-color: #ff6b7a;
            transform: translateX(-10px) scale(1.02);
        }

        .admin-panel-link:hover::after {
            opacity: 1;
            transform: scale(1.1);
            background: linear-gradient(45deg, #ff6b7a, #ffc107);
        }

        /* زر لوحة التحكم الإدارية المميز */
        .admin-control-section {
            padding: 0 15px;
            position: relative;
        }

        .admin-control-btn {
            display: block;
            background: linear-gradient(135deg, #dc3545, #c82333);
            border: 2px solid rgba(255, 255, 255, 0.2);
            border-radius: 15px;
            padding: 15px;
            text-decoration: none;
            color: white;
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
            overflow: hidden;
            box-shadow: 0 8px 25px rgba(220, 53, 69, 0.3);
        }

        .admin-control-btn::after {
            content: 'ADMIN';
            position: absolute;
            top: -5px;
            right: -5px;
            background: linear-gradient(45deg, #ffd700, #ffed4e);
            color: #dc3545;
            font-size: 0.6rem;
            font-weight: bold;
            padding: 3px 8px;
            border-radius: 10px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
            animation: admin-badge-glow 2s infinite alternate;
            z-index: 10;
        }

        @keyframes admin-badge-glow {
            0% {
                opacity: 0.9;
                transform: scale(1);
                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
            }
            100% {
                opacity: 1;
                transform: scale(1.05);
                box-shadow: 0 4px 12px rgba(255, 215, 0, 0.5);
            }
        }

        .admin-control-btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            transition: left 0.6s;
        }

        .admin-control-btn:hover {
            transform: translateY(-3px) scale(1.02);
            box-shadow: 0 12px 35px rgba(220, 53, 69, 0.4);
            border-color: rgba(255, 255, 255, 0.4);
            background: linear-gradient(135deg, #e74c3c, #dc3545);
            color: white;
            text-decoration: none;
        }

        .admin-control-btn:hover::before {
            left: 100%;
        }

        .admin-btn-content {
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .admin-btn-icon {
            width: 45px;
            height: 45px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
            transition: all 0.3s ease;
        }

        .admin-control-btn:hover .admin-btn-icon {
            background: rgba(255, 255, 255, 0.3);
            transform: scale(1.1) rotate(5deg);
        }

        .admin-btn-text {
            flex: 1;
        }

        .admin-btn-title {
            font-weight: 700;
            font-size: 1rem;
            margin-bottom: 2px;
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
        }

        .admin-btn-subtitle {
            font-size: 0.75rem;
            opacity: 0.9;
            font-weight: 400;
        }

        .admin-btn-arrow {
            font-size: 1.2rem;
            transition: all 0.3s ease;
            opacity: 0.8;
        }

        .admin-control-btn:hover .admin-btn-arrow {
            transform: translateX(5px);
            opacity: 1;
        }

        /* تأثير نبضة للزر */
        .admin-control-btn {
            animation: admin-btn-pulse 3s infinite;
        }

        @keyframes admin-btn-pulse {
            0%, 100% {
                box-shadow: 0 8px 25px rgba(220, 53, 69, 0.3);
            }
            50% {
                box-shadow: 0 8px 25px rgba(220, 53, 69, 0.5), 0 0 20px rgba(220, 53, 69, 0.3);
            }
        }

        .sidebar .nav-link[href="/dashboard"] {
            background: linear-gradient(135deg, rgba(108, 117, 125, 0.2), rgba(108, 117, 125, 0.1));
            border-left: 4px solid #6c757d;
            box-shadow: inset 0 0 20px rgba(108, 117, 125, 0.1);
        }

        .sidebar .nav-link[href="/persons"] {
            background: linear-gradient(135deg, rgba(23, 162, 184, 0.2), rgba(23, 162, 184, 0.1));
            border-left: 4px solid #17a2b8;
            box-shadow: inset 0 0 20px rgba(23, 162, 184, 0.1);
        }

        .sidebar .nav-link[href="/recognition"] {
            background: linear-gradient(135deg, rgba(40, 167, 69, 0.2), rgba(40, 167, 69, 0.1));
            border-left: 4px solid #28a745;
            box-shadow: inset 0 0 20px rgba(40, 167, 69, 0.1);
        }

        .sidebar .nav-link[href="/camera-test"] {
            background: linear-gradient(135deg, rgba(255, 193, 7, 0.2), rgba(255, 193, 7, 0.1));
            border-left: 4px solid #ffc107;
            box-shadow: inset 0 0 20px rgba(255, 193, 7, 0.1);
        }

        .sidebar .nav-link[href="/logs"] {
            background: linear-gradient(135deg, rgba(220, 53, 69, 0.2), rgba(220, 53, 69, 0.1));
            border-left: 4px solid #dc3545;
            box-shadow: inset 0 0 20px rgba(220, 53, 69, 0.1);
        }

        .sidebar .nav-link[href="/settings"] {
            background: linear-gradient(135deg, rgba(102, 16, 242, 0.2), rgba(102, 16, 242, 0.1));
            border-left: 4px solid #6610f2;
            box-shadow: inset 0 0 20px rgba(102, 16, 242, 0.1);
        }

        .sidebar-header {
            position: relative;
            z-index: 2;
        }

        .sidebar-logo i {
            animation: pulse 2s infinite;
            filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.3));
        }

        .sidebar-title {
            color: white;
            font-weight: 700;
            font-size: 1.1rem;
            margin: 0;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
            letter-spacing: 1px;
        }

        .sidebar-subtitle {
            color: rgba(255, 255, 255, 0.8);
            font-size: 0.85rem;
            font-weight: 400;
            margin-top: 5px;
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
        }

        .sidebar-divider {
            border: none;
            height: 2px;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.5), transparent);
            margin: 15px 0;
        }

        @keyframes pulse {
            0%, 100% { opacity: 1; transform: scale(1); }
            50% { opacity: 0.8; transform: scale(1.05); }
        }

        .nav-section {
            margin-bottom: 25px;
            position: relative;
        }

        .nav-section-title {
            color: rgba(255, 255, 255, 0.6);
            font-size: 0.75rem;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 1.5px;
            margin: 0 20px 10px 20px;
            padding-bottom: 5px;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
        }

        .nav-section:last-child {
            margin-bottom: 0;
        }

        .nav-section .nav-link {
            margin-bottom: 5px;
        }

        .nav-section .nav-link:last-child {
            margin-bottom: 0;
        }

        /* تأثيرات إضافية للقائمة */
        .sidebar {
            animation: slideInLeft 0.5s ease-out;
        }

        @keyframes slideInLeft {
            from {
                transform: translateX(-100%);
                opacity: 0;
            }
            to {
                transform: translateX(0);
                opacity: 1;
            }
        }

        .nav-link {
            animation: fadeInUp 0.6s ease-out;
            animation-fill-mode: both;
        }

        .nav-section:nth-child(1) .nav-link { animation-delay: 0.1s; }
        .nav-section:nth-child(2) .nav-link { animation-delay: 0.2s; }
        .nav-section:nth-child(3) .nav-link { animation-delay: 0.3s; }
        .nav-section:nth-child(4) .nav-link { animation-delay: 0.4s; }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        /* تحسين responsive */
        @media (max-width: 768px) {
            .sidebar {
                min-height: auto;
                position: relative;
            }

            .nav-section-title {
                font-size: 0.7rem;
                margin: 0 15px 8px 15px;
            }

            .sidebar .nav-link {
                padding: 12px 15px;
                margin: 5px 10px;
                font-size: 0.9rem;
            }

            .sidebar .nav-link i {
                font-size: 1.1em;
                width: 20px;
                margin-right: 10px;
            }
        }
        
        .card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            transition: transform 0.3s ease;
        }
        
        .card:hover {
            transform: translateY(-5px);
        }
        
        .card-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 15px 15px 0 0 !important;
            font-weight: bold;
        }
        
        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 25px;
            padding: 10px 25px;
            transition: all 0.3s ease;
        }
        
        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
        }
        
        .status-indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            display: inline-block;
            margin-left: 8px;
        }
        
        .status-online {
            background-color: #28a745;
            animation: pulse 2s infinite;
        }
        
        .status-offline {
            background-color: #dc3545;
        }
        
        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }
        
        .metric-card {
            background: white;
            border-radius: 15px;
            padding: 20px;
            text-align: center;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }
        
        .metric-value {
            font-size: 2.5rem;
            font-weight: bold;
            color: #667eea;
        }
        
        .metric-label {
            color: #6c757d;
            font-size: 0.9rem;
        }
        
        .alert-custom {
            border-radius: 15px;
            border: none;
        }
        
        .table-custom {
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }
        
        .loading-spinner {
            display: none;
        }
        
        .loading .loading-spinner {
            display: inline-block;
        }
    </style>
    
    {% block extra_css %}{% endblock %}
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container-fluid">
            <a class="navbar-brand" href="/">
                <i class="bi bi-camera-video"></i>
                نظام التعرف على الوجوه الاحترافي
            </a>
            
            <div class="navbar-nav ms-auto">
                <div class="nav-item">
                    <span class="navbar-text">
                        حالة النظام: 
                        <span id="system-status" class="fw-bold">
                            <span class="status-indicator status-offline"></span>
                            غير متصل
                        </span>
                    </span>
                </div>
            </div>
        </div>
    </nav>

    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <div class="col-md-2 sidebar p-0">
                <div class="p-3">
                    <div class="sidebar-header text-center mb-4">
                        <div class="sidebar-logo mb-3">
                            <i class="bi bi-camera-video-fill" style="font-size: 2.5rem; color: rgba(255, 255, 255, 0.9);"></i>
                        </div>
                        <h5 class="sidebar-title">القائمة الرئيسية</h5>
                        <div class="sidebar-subtitle">نظام التعرف على الوجوه</div>
                        <hr class="sidebar-divider">
                    </div>
                    <nav class="nav flex-column">
                        <!-- زر لوحة التحكم الإدارية المميز -->
                        <div class="admin-control-section mb-4">
                            <a href="/admin" class="admin-control-btn">
                                <div class="admin-btn-content">
                                    <div class="admin-btn-icon">
                                        <i class="bi bi-shield-check"></i>
                                    </div>
                                    <div class="admin-btn-text">
                                        <div class="admin-btn-title">لوحة التحكم الإدارية</div>
                                        <div class="admin-btn-subtitle">إدارة النظام والمراقبة</div>
                                    </div>
                                    <div class="admin-btn-arrow">
                                        <i class="bi bi-arrow-right"></i>
                                    </div>
                                </div>
                            </a>
                        </div>

                        <!-- القسم الرئيسي -->
                        <div class="nav-section">
                            <div class="nav-section-title">التنقل الرئيسي</div>
                            <a class="nav-link" href="/">
                                <i class="bi bi-house me-2"></i>
                                الصفحة الرئيسية
                            </a>
                            <a class="nav-link admin-panel-link" href="/admin">
                                <span class="admin-icon me-2">
                                    <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                                        <path d="M12 1L3 5V11C3 16.55 6.84 21.74 12 23C17.16 21.74 21 16.55 21 11V5L12 1ZM12 7C13.1 7 14 7.9 14 9S13.1 11 12 11 10 10.1 10 9 10.9 7 12 7ZM18 17H6V15.5C6 13.56 9.97 12.5 12 12.5S18 13.56 18 15.5V17Z"/>
                                    </svg>
                                </span>
                                لوحة التحكم الإدارية
                            </a>
                            <a class="nav-link" href="/dashboard">
                                <i class="bi bi-speedometer2 me-2"></i>
                                لوحة التحكم العامة
                            </a>
                        </div>

                        <!-- قسم إدارة البيانات -->
                        <div class="nav-section">
                            <div class="nav-section-title">إدارة البيانات</div>
                            <a class="nav-link" href="/persons">
                                <i class="bi bi-people me-2"></i>
                                إدارة الأشخاص
                            </a>
                        </div>

                        <!-- قسم التعرف -->
                        <div class="nav-section">
                            <div class="nav-section-title">نظام التعرف</div>
                            <a class="nav-link" href="/recognition">
                                <i class="bi bi-camera-video me-2"></i>
                                التعرف على الوجوه
                            </a>
                            <a class="nav-link" href="/camera-test">
                                <i class="bi bi-camera me-2"></i>
                                تشخيص الكاميرا
                            </a>
                        </div>

                        <!-- قسم المراقبة والإعدادات -->
                        <div class="nav-section">
                            <div class="nav-section-title">المراقبة والإعدادات</div>
                            <a class="nav-link" href="/logs">
                                <i class="bi bi-journal-text me-2"></i>
                                السجلات
                            </a>
                            <a class="nav-link" href="/settings">
                                <i class="bi bi-gear me-2"></i>
                                الإعدادات
                            </a>
                        </div>
                    </nav>
                </div>
            </div>

            <!-- Main Content -->
            <div class="col-md-10 p-4">
                {% block content %}{% endblock %}
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- Custom JavaScript -->
    <script>
        // Socket.IO connection
        const socket = io();
        
        // System status management
        socket.on('connect', function() {
            updateSystemStatus(true);
            socket.emit('request_live_data');
        });
        
        socket.on('disconnect', function() {
            updateSystemStatus(false);
        });
        
        socket.on('live_data', function(data) {
            updateLiveData(data);
        });
        
        function updateSystemStatus(isOnline) {
            const statusElement = document.getElementById('system-status');
            if (isOnline) {
                statusElement.innerHTML = '<span class="status-indicator status-online"></span>متصل';
            } else {
                statusElement.innerHTML = '<span class="status-indicator status-offline"></span>غير متصل';
            }
        }
        
        function updateLiveData(data) {
            // Update live data in the interface
            if (typeof updateDashboard === 'function') {
                updateDashboard(data);
            }
        }
        
        // Utility functions
        function showAlert(message, type = 'info') {
            const alertDiv = document.createElement('div');
            alertDiv.className = `alert alert-${type} alert-dismissible fade show alert-custom`;
            alertDiv.innerHTML = `
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;
            
            const container = document.querySelector('.container-fluid .row .col-md-10');
            container.insertBefore(alertDiv, container.firstChild);
            
            // Auto dismiss after 5 seconds
            setTimeout(() => {
                if (alertDiv.parentNode) {
                    alertDiv.remove();
                }
            }, 5000);
        }
        
        function formatDateTime(dateString) {
            const date = new Date(dateString);
            return date.toLocaleString('ar-SA');
        }
        
        function formatNumber(num) {
            return new Intl.NumberFormat('ar-SA').format(num);
        }
        
        // API helper functions
        async function apiCall(url, options = {}) {
            try {
                const response = await fetch(url, {
                    headers: {
                        'Content-Type': 'application/json',
                        ...options.headers
                    },
                    ...options
                });
                
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                
                return await response.json();
            } catch (error) {
                console.error('API call failed:', error);
                showAlert(`خطأ في الاتصال: ${error.message}`, 'danger');
                throw error;
            }
        }
        
        // Set active navigation link
        document.addEventListener('DOMContentLoaded', function() {
            const currentPath = window.location.pathname;
            const navLinks = document.querySelectorAll('.sidebar .nav-link');
            
            navLinks.forEach(link => {
                if (link.getAttribute('href') === currentPath) {
                    link.classList.add('active');
                }
            });
        });
    </script>

    <script src="{{ url_for('static', filename='js/force_refresh.js') }}?v=12345"></script>

    {% block extra_js %}{% endblock %}
</body>
</html>
