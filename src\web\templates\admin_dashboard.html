{% extends "base.html" %}

{% block title %}لوحة التحكم الإدارية - نظام التعرف على الوجوه{% endblock %}

{% block content %}
<!-- Header -->
<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h2><i class="bi bi-shield-lock me-2"></i>لوحة التحكم الإدارية</h2>
        <p class="text-muted mb-0">مرحباً {{ current_user.full_name or current_user.username }} - {{ current_user.national_id }}</p>
    </div>
    <div>
        <span class="badge bg-danger fs-6">
            <i class="bi bi-person-gear me-1"></i>مدير النظام
        </span>
        <a href="/logout" class="btn btn-outline-danger ms-2">
            <i class="bi bi-box-arrow-right me-1"></i>تسجيل الخروج
        </a>
    </div>
</div>

<!-- Quick Stats -->
<div class="row mb-4">
    <div class="col-md-3 mb-3">
        <div class="card bg-primary text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 class="card-title">{{ stats.total_persons or 0 }}</h4>
                        <p class="card-text">إجمالي الأشخاص</p>
                    </div>
                    <div class="align-self-center">
                        <i class="bi bi-people fs-1"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-3 mb-3">
        <div class="card bg-success text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 class="card-title">{{ stats.active_persons or 0 }}</h4>
                        <p class="card-text">الأشخاص النشطون</p>
                    </div>
                    <div class="align-self-center">
                        <i class="bi bi-person-check fs-1"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-3 mb-3">
        <div class="card bg-info text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 class="card-title">{{ stats.today_recognitions or 0 }}</h4>
                        <p class="card-text">تعرف اليوم</p>
                    </div>
                    <div class="align-self-center">
                        <i class="bi bi-camera fs-1"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-3 mb-3">
        <div class="card bg-warning text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 class="card-title">{{ stats.total_users or 1 }}</h4>
                        <p class="card-text">المستخدمون</p>
                    </div>
                    <div class="align-self-center">
                        <i class="bi bi-person-gear fs-1"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Quick Actions -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="bi bi-lightning me-2"></i>الإجراءات السريعة</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3 mb-3">
                        <a href="/persons" class="btn btn-outline-primary w-100 h-100 d-flex flex-column align-items-center justify-content-center p-3">
                            <i class="bi bi-people fs-1 mb-2"></i>
                            <span>إدارة الأشخاص</span>
                            <small class="text-muted">{{ stats.total_persons or 0 }} شخص</small>
                        </a>
                    </div>
                    <div class="col-md-3 mb-3">
                        <a href="/recognition" class="btn btn-outline-success w-100 h-100 d-flex flex-column align-items-center justify-content-center p-3">
                            <i class="bi bi-camera fs-1 mb-2"></i>
                            <span>التعرف على الوجوه</span>
                            <small class="text-muted">بدء النظام</small>
                        </a>
                    </div>
                    <div class="col-md-3 mb-3">
                        <a href="/logs" class="btn btn-outline-info w-100 h-100 d-flex flex-column align-items-center justify-content-center p-3">
                            <i class="bi bi-journal-text fs-1 mb-2"></i>
                            <span>السجلات</span>
                            <small class="text-muted">عرض النشاط</small>
                        </a>
                    </div>
                    <div class="col-md-3 mb-3">
                        <a href="/admin/users" class="btn btn-outline-warning w-100 h-100 d-flex flex-column align-items-center justify-content-center p-3">
                            <i class="bi bi-gear fs-1 mb-2"></i>
                            <span>إدارة المستخدمين</span>
                            <small class="text-muted">{{ stats.total_users or 1 }} مستخدم</small>
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Recent Activity & System Info -->
<div class="row">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="bi bi-clock-history me-2"></i>النشاط الأخير</h5>
            </div>
            <div class="card-body">
                {% if recent_activity %}
                    <div class="list-group list-group-flush">
                        {% for activity in recent_activity %}
                        <div class="list-group-item d-flex justify-content-between align-items-center">
                            <div>
                                <h6 class="mb-1">{{ activity.person_name or 'غير معروف' }}</h6>
                                <p class="mb-1 text-muted">تم التعرف عليه بثقة {{ "%.1f"|format(activity.confidence * 100) }}%</p>
                                <small class="text-muted">{{ activity.timestamp }}</small>
                            </div>
                            <span class="badge bg-success rounded-pill">
                                <i class="bi bi-check-circle"></i>
                            </span>
                        </div>
                        {% endfor %}
                    </div>
                    <div class="text-center mt-3">
                        <a href="/logs" class="btn btn-outline-primary">
                            <i class="bi bi-eye me-1"></i>عرض جميع السجلات
                        </a>
                    </div>
                {% else %}
                    <div class="text-center text-muted py-4">
                        <i class="bi bi-clock-history display-4"></i>
                        <h5 class="mt-3">لا يوجد نشاط حديث</h5>
                        <p>ابدأ بتشغيل نظام التعرف على الوجوه</p>
                        <a href="/recognition" class="btn btn-primary">
                            <i class="bi bi-camera me-1"></i>بدء التعرف
                        </a>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
    
    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="bi bi-info-circle me-2"></i>معلومات الجلسة</h5>
            </div>
            <div class="card-body">
                <div class="mb-3">
                    <strong>حالة النظام:</strong>
                    <span class="badge bg-success ms-2">
                        <i class="bi bi-check-circle me-1"></i>يعمل
                    </span>
                </div>
                
                <div class="mb-3">
                    <strong>المستخدم:</strong>
                    <br>
                    <span class="text-primary">{{ current_user.username }}</span>
                </div>
                
                <div class="mb-3">
                    <strong>رقم الهوية:</strong>
                    <br>
                    <code>{{ current_user.national_id }}</code>
                </div>
                
                <div class="mb-3">
                    <strong>الصلاحيات:</strong>
                    <br>
                    <span class="badge bg-danger">
                        <i class="bi bi-shield-lock me-1"></i>مدير النظام
                    </span>
                </div>
                
                <div class="mb-3">
                    <strong>وقت تسجيل الدخول:</strong>
                    <br>
                    <small class="text-muted">{{ current_user.login_time | timestamp_to_date if current_user.login_time else 'الآن' }}</small>
                </div>
                
                <hr>
                
                <div class="d-grid gap-2">
                    <a href="/admin/users" class="btn btn-outline-primary">
                        <i class="bi bi-people me-2"></i>إدارة المستخدمين
                    </a>
                    <a href="/admin/settings" class="btn btn-outline-secondary">
                        <i class="bi bi-gear me-2"></i>إعدادات النظام
                    </a>
                    <a href="/logout" class="btn btn-outline-danger">
                        <i class="bi bi-box-arrow-right me-2"></i>تسجيل الخروج
                    </a>
                </div>
            </div>
        </div>
        
        <!-- System Status -->
        <div class="card mt-3">
            <div class="card-header">
                <h6 class="mb-0"><i class="bi bi-cpu me-2"></i>حالة النظام</h6>
            </div>
            <div class="card-body">
                <div class="d-flex justify-content-between mb-2">
                    <span>قاعدة البيانات:</span>
                    <span class="badge bg-success">متصلة</span>
                </div>
                <div class="d-flex justify-content-between mb-2">
                    <span>خادم الويب:</span>
                    <span class="badge bg-success">يعمل</span>
                </div>
                <div class="d-flex justify-content-between mb-2">
                    <span>نظام التعرف:</span>
                    <span class="badge bg-warning">جاهز</span>
                </div>
                <div class="d-flex justify-content-between">
                    <span>الكاميرا:</span>
                    <span class="badge bg-secondary">غير متصلة</span>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// تحديث الإحصائيات كل 30 ثانية
setInterval(function() {
    fetch('/api/dashboard/stats')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                console.log('تم تحديث إحصائيات لوحة التحكم');
                // يمكن تحديث الإحصائيات هنا إذا لزم الأمر
            }
        })
        .catch(error => {
            console.error('خطأ في تحديث الإحصائيات:', error);
        });
}, 30000);

// تسجيل تحميل لوحة التحكم
console.log('🔐 تم تحميل لوحة التحكم الإدارية بنجاح');
console.log('👤 المستخدم الحالي:', '{{ current_user.username }}');
console.log('🆔 رقم الهوية:', '{{ current_user.national_id }}');
console.log('🔑 الصلاحيات:', '{{ current_user.role }}');
</script>
{% endblock %}
