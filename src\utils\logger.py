"""
نظام التسجيل المتقدم
Advanced Logging System
"""

import logging
import logging.handlers
import os
import sys
from datetime import datetime
from pathlib import Path
from typing import Optional


class ColoredFormatter(logging.Formatter):
    """مُنسق ملون للرسائل في وحدة التحكم"""
    
    # ألوان ANSI
    COLORS = {
        'DEBUG': '\033[36m',      # سماوي
        'INFO': '\033[32m',       # أخضر
        'WARNING': '\033[33m',    # أصفر
        'ERROR': '\033[31m',      # أحمر
        'CRITICAL': '\033[35m',   # بنفسجي
        'RESET': '\033[0m'        # إعادة تعيين
    }
    
    def format(self, record):
        # إضافة اللون للرسالة
        color = self.COLORS.get(record.levelname, self.COLORS['RESET'])
        record.levelname = f"{color}{record.levelname}{self.COLORS['RESET']}"
        return super().format(record)


class FaceRecognitionLogger:
    """فئة مخصصة لتسجيل أحداث نظام التعرف على الوجوه"""
    
    def __init__(self, name: str = "FaceRecognition", log_dir: str = "data/logs"):
        self.name = name
        self.log_dir = Path(log_dir)
        self.log_dir.mkdir(parents=True, exist_ok=True)
        
        # إنشاء logger
        self.logger = logging.getLogger(name)
        self.logger.setLevel(logging.DEBUG)
        
        # تجنب إضافة handlers متعددة
        if not self.logger.handlers:
            self._setup_handlers()
    
    def _setup_handlers(self):
        """إعداد معالجات التسجيل"""
        
        # 1. معالج الملف الرئيسي (جميع الرسائل)
        main_log_file = self.log_dir / "face_recognition.log"
        file_handler = logging.handlers.RotatingFileHandler(
            main_log_file,
            maxBytes=10*1024*1024,  # 10 MB
            backupCount=5,
            encoding='utf-8'
        )
        file_formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(funcName)s:%(lineno)d - %(message)s',
            datefmt='%Y-%m-%d %H:%M:%S'
        )
        file_handler.setFormatter(file_formatter)
        file_handler.setLevel(logging.DEBUG)
        self.logger.addHandler(file_handler)
        
        # 2. معالج ملف الأخطاء فقط
        error_log_file = self.log_dir / "errors.log"
        error_handler = logging.handlers.RotatingFileHandler(
            error_log_file,
            maxBytes=5*1024*1024,  # 5 MB
            backupCount=3,
            encoding='utf-8'
        )
        error_handler.setFormatter(file_formatter)
        error_handler.setLevel(logging.ERROR)
        self.logger.addHandler(error_handler)
        
        # 3. معالج وحدة التحكم (ملون)
        console_handler = logging.StreamHandler(sys.stdout)
        console_formatter = ColoredFormatter(
            '%(asctime)s - %(levelname)s - %(message)s',
            datefmt='%H:%M:%S'
        )
        console_handler.setFormatter(console_formatter)
        console_handler.setLevel(logging.INFO)
        self.logger.addHandler(console_handler)
        
        # 4. معالج أحداث التعرف (ملف منفصل)
        recognition_log_file = self.log_dir / "recognition_events.log"
        recognition_handler = logging.handlers.RotatingFileHandler(
            recognition_log_file,
            maxBytes=20*1024*1024,  # 20 MB
            backupCount=10,
            encoding='utf-8'
        )
        recognition_formatter = logging.Formatter(
            '%(asctime)s - %(message)s',
            datefmt='%Y-%m-%d %H:%M:%S'
        )
        recognition_handler.setFormatter(recognition_formatter)
        recognition_handler.setLevel(logging.INFO)
        
        # إنشاء logger منفصل لأحداث التعرف
        self.recognition_logger = logging.getLogger(f"{self.name}.Recognition")
        self.recognition_logger.setLevel(logging.INFO)
        self.recognition_logger.addHandler(recognition_handler)
        self.recognition_logger.propagate = False
    
    def debug(self, message: str):
        """تسجيل رسالة تصحيح"""
        self.logger.debug(message)
    
    def info(self, message: str):
        """تسجيل رسالة معلومات"""
        self.logger.info(message)
    
    def warning(self, message: str):
        """تسجيل رسالة تحذير"""
        self.logger.warning(message)
    
    def error(self, message: str):
        """تسجيل رسالة خطأ"""
        self.logger.error(message)
    
    def critical(self, message: str):
        """تسجيل رسالة خطأ حرج"""
        self.logger.critical(message)
    
    def log_recognition_event(self, person_name: str, confidence: float, 
                            timestamp: Optional[datetime] = None):
        """تسجيل حدث تعرف على شخص"""
        if timestamp is None:
            timestamp = datetime.now()
        
        message = f"تم التعرف على: {person_name} - الدقة: {confidence:.2f}% - الوقت: {timestamp}"
        self.recognition_logger.info(message)
    
    def log_unknown_face(self, timestamp: Optional[datetime] = None):
        """تسجيل اكتشاف وجه غير معروف"""
        if timestamp is None:
            timestamp = datetime.now()
        
        message = f"تم اكتشاف وجه غير معروف - الوقت: {timestamp}"
        self.recognition_logger.info(message)
    
    def log_system_event(self, event_type: str, details: str):
        """تسجيل أحداث النظام"""
        message = f"[{event_type}] {details}"
        self.logger.info(message)
    
    def log_performance_metrics(self, fps: float, processing_time: float, 
                              faces_detected: int):
        """تسجيل مقاييس الأداء"""
        message = (f"الأداء - FPS: {fps:.1f}, وقت المعالجة: {processing_time:.3f}s, "
                  f"الوجوه المكتشفة: {faces_detected}")
        self.logger.debug(message)
    
    def log_camera_event(self, event: str):
        """تسجيل أحداث الكاميرا"""
        self.logger.info(f"الكاميرا: {event}")
    
    def log_database_event(self, operation: str, details: str):
        """تسجيل أحداث قاعدة البيانات"""
        self.logger.info(f"قاعدة البيانات - {operation}: {details}")
    
    def log_security_alert(self, alert_type: str, details: str):
        """تسجيل تنبيهات الأمان"""
        message = f"تنبيه أمني - {alert_type}: {details}"
        self.logger.warning(message)
        
        # حفظ في ملف تنبيهات منفصل
        alert_file = self.log_dir / "security_alerts.log"
        with open(alert_file, 'a', encoding='utf-8') as f:
            timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            f.write(f"{timestamp} - {message}\n")


def setup_logger(name: str = "FaceRecognition", 
                log_dir: str = "data/logs") -> FaceRecognitionLogger:
    """إعداد وإرجاع logger للنظام"""
    return FaceRecognitionLogger(name, log_dir)


# إنشاء logger عام للنظام
system_logger = setup_logger()


def get_logger() -> FaceRecognitionLogger:
    """الحصول على logger النظام"""
    return system_logger
