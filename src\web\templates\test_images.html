{% extends "base.html" %}

{% block title %}اختبار الصور - نظام التعرف على الوجوه{% endblock %}

{% block content %}
<div class="container mt-4">
    <h2><i class="bi bi-image me-2"></i>اختبار عرض الصور</h2>
    
    <div class="row">
        {% for person in persons %}
        <div class="col-md-3 mb-4">
            <div class="card">
                <div class="card-header">
                    <h6 class="mb-0">{{ person.name }}</h6>
                </div>
                <div class="card-body text-center">
                    {% if person.image_path %}
                        {% set filename = person.image_path.replace('\\', '/').split('/')[-1] %}
                        {% set is_default = '_default.' in filename %}
                        
                        <div class="mb-3">
                            <img src="/static/persons/{{ filename }}" 
                                 alt="{{ person.name }}" 
                                 class="img-fluid rounded"
                                 style="max-width: 150px; max-height: 150px; border: 2px solid {{ '#28a745' if not is_default else '#ffc107' }};"
                                 onerror="this.style.display='none'; this.nextElementSibling.style.display='block';">
                            <div style="display: none; padding: 20px; background: #f8f9fa; border: 2px dashed #ccc;">
                                <i class="bi bi-exclamation-triangle text-warning"></i><br>
                                فشل تحميل الصورة
                            </div>
                        </div>
                        
                        <div class="small">
                            <strong>اسم الملف:</strong><br>
                            <code>{{ filename }}</code><br><br>
                            <strong>المسار الكامل:</strong><br>
                            <code style="font-size: 10px;">{{ person.image_path }}</code><br><br>
                            <strong>النوع:</strong><br>
                            <span class="badge {{ 'bg-success' if not is_default else 'bg-warning' }}">
                                {{ 'صورة حقيقية' if not is_default else 'صورة افتراضية' }}
                            </span><br><br>
                            <strong>رابط الصورة:</strong><br>
                            <a href="/static/persons/{{ filename }}" target="_blank" class="btn btn-sm btn-outline-primary">
                                <i class="bi bi-box-arrow-up-right"></i> فتح الصورة
                            </a>
                        </div>
                    {% else %}
                        <div class="text-muted">
                            <i class="bi bi-image display-4"></i><br>
                            لا توجد صورة
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
        {% endfor %}
    </div>
    
    <div class="mt-4">
        <h4>معلومات التشخيص</h4>
        <div class="card">
            <div class="card-body">
                <p><strong>عدد الأشخاص:</strong> {{ persons|length }}</p>
                <p><strong>الصور المتاحة:</strong></p>
                <ul>
                    {% for person in persons %}
                        {% if person.image_path %}
                            {% set filename = person.image_path.replace('\\', '/').split('/')[-1] %}
                            <li>
                                <strong>{{ person.name }}:</strong> 
                                <a href="/static/persons/{{ filename }}" target="_blank">{{ filename }}</a>
                                {% if '_default.' in filename %}
                                    <span class="badge bg-warning">افتراضية</span>
                                {% else %}
                                    <span class="badge bg-success">حقيقية</span>
                                {% endif %}
                            </li>
                        {% else %}
                            <li><strong>{{ person.name }}:</strong> <span class="text-muted">لا توجد صورة</span></li>
                        {% endif %}
                    {% endfor %}
                </ul>
            </div>
        </div>
    </div>
    
    <div class="mt-3">
        <a href="/persons" class="btn btn-primary">
            <i class="bi bi-arrow-left"></i> العودة إلى قائمة الأشخاص
        </a>
    </div>
</div>

<script>
console.log('🖼️ صفحة اختبار الصور');
console.log('📊 عدد الأشخاص:', {{ persons|length }});

// اختبار تحميل الصور
document.addEventListener('DOMContentLoaded', function() {
    const images = document.querySelectorAll('img[src*="/static/persons/"]');
    console.log('🔍 عدد الصور المطلوب تحميلها:', images.length);
    
    images.forEach((img, index) => {
        img.onload = function() {
            console.log(`✅ تم تحميل الصورة ${index + 1}: ${this.src}`);
        };
        
        img.onerror = function() {
            console.error(`❌ فشل تحميل الصورة ${index + 1}: ${this.src}`);
        };
    });
});
</script>
{% endblock %}
