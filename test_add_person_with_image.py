#!/usr/bin/env python3
"""
اختبار إضافة شخص مع صورة
"""

import requests
import os
from pathlib import Path

def test_add_person_with_image():
    """اختبار إضافة شخص مع صورة"""
    print("🧪 اختبار إضافة شخص مع صورة...")
    
    # البحث عن صورة في مجلد الأشخاص
    persons_dir = Path("data/persons")
    image_files = list(persons_dir.glob("*.jpg")) + list(persons_dir.glob("*.png"))
    
    if not image_files:
        print("❌ لا توجد صور في مجلد data/persons")
        return
    
    # استخدام أول صورة متاحة
    image_path = image_files[0]
    print(f"📸 استخدام الصورة: {image_path}")
    
    try:
        # إعداد البيانات
        files = {
            'image': ('test_image.jpg', open(image_path, 'rb'), 'image/jpeg')
        }
        
        data = {
            'name': 'Test Person With Image',
            'full_name': 'Test Person Full Name',
            'email': '<EMAIL>',
            'phone': '987654321',
            'department': 'Testing',
            'position': 'Test Engineer',
            'notes': 'شخص تجريبي مع صورة'
        }
        
        # إرسال الطلب
        response = requests.post(
            'http://localhost:5000/api/persons',
            files=files,
            data=data
        )
        
        print(f"📊 كود الاستجابة: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ تم إضافة الشخص مع الصورة بنجاح!")
            print(f"🆔 معرف الشخص: {result.get('id')}")
            print(f"📄 الرسالة: {result.get('message')}")
        else:
            print(f"❌ فشل في إضافة الشخص: {response.status_code}")
            try:
                error = response.json()
                print(f"📄 رسالة الخطأ: {error.get('error')}")
            except:
                print(f"📄 محتوى الخطأ: {response.text}")
        
        # إغلاق الملف
        files['image'][1].close()
        
    except Exception as e:
        print(f"❌ خطأ في اختبار إضافة الشخص مع الصورة: {e}")

def test_list_persons_after_add():
    """اختبار قائمة الأشخاص بعد الإضافة"""
    print("\n🧪 اختبار قائمة الأشخاص بعد الإضافة...")
    
    try:
        response = requests.get('http://localhost:5000/api/persons')
        
        if response.status_code == 200:
            persons = response.json()
            print(f"✅ تم تحميل {len(persons)} شخص")
            
            # عرض آخر 3 أشخاص مضافين
            for person in persons[-3:]:
                print(f"   - {person.get('name')} ({person.get('email', 'لا يوجد إيميل')})")
                if person.get('image_path'):
                    print(f"     📸 صورة: {person.get('image_path')}")
        else:
            print(f"❌ فشل في تحميل قائمة الأشخاص: {response.status_code}")
            
    except Exception as e:
        print(f"❌ خطأ في تحميل قائمة الأشخاص: {e}")

if __name__ == "__main__":
    print("🎯 اختبار إضافة شخص مع صورة")
    print("=" * 50)
    
    test_add_person_with_image()
    test_list_persons_after_add()
    
    print("\n" + "=" * 50)
    print("✅ انتهى الاختبار")
