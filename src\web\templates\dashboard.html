{% extends "base.html" %}

{% block title %}لوحة التحكم - نظام التعرف على الوجوه{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h2><i class="bi bi-speedometer2 me-2"></i>لوحة التحكم</h2>
    <div>
        <button id="start-system-btn" class="btn btn-success me-2">
            <i class="bi bi-play-fill"></i> تشغيل النظام
        </button>
        <button id="stop-system-btn" class="btn btn-danger">
            <i class="bi bi-stop-fill"></i> إيقاف النظام
        </button>
    </div>
</div>

<!-- System Metrics -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="metric-card">
            <div class="metric-value" id="fps-metric">0</div>
            <div class="metric-label">إطار في الثانية</div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="metric-card">
            <div class="metric-value" id="faces-detected-metric">0</div>
            <div class="metric-label">الوجوه المكتشفة</div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="metric-card">
            <div class="metric-value" id="faces-recognized-metric">0</div>
            <div class="metric-label">الوجوه المعروفة</div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="metric-card">
            <div class="metric-value" id="uptime-metric">00:00:00</div>
            <div class="metric-label">وقت التشغيل</div>
        </div>
    </div>
</div>

<!-- Charts Row -->
<div class="row mb-4">
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="bi bi-graph-up me-2"></i>الأداء المباشر</h5>
            </div>
            <div class="card-body">
                <canvas id="performance-chart" height="200"></canvas>
            </div>
        </div>
    </div>
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="bi bi-pie-chart me-2"></i>إحصائيات التعرف</h5>
            </div>
            <div class="card-body">
                <canvas id="recognition-chart" height="200"></canvas>
            </div>
        </div>
    </div>
</div>

<!-- System Information -->
<div class="row mb-4">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="bi bi-info-circle me-2"></i>معلومات النظام</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <p><strong>حالة الكاميرا:</strong> <span id="camera-status" class="badge bg-secondary">غير معروف</span></p>
                        <p><strong>استخدام المعالج:</strong> <span id="cpu-usage">0%</span></p>
                        <p><strong>استخدام الذاكرة:</strong> <span id="memory-usage">0%</span></p>
                    </div>
                    <div class="col-md-6">
                        <p><strong>عدد الأشخاص المسجلين:</strong> <span id="registered-persons">0</span></p>
                        <p><strong>آخر تعرف:</strong> <span id="last-recognition">لا يوجد</span></p>
                        <p><strong>إجمالي التعرفات اليوم:</strong> <span id="today-recognitions">0</span></p>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="bi bi-shield-check me-2"></i>حالة الأمان</h5>
            </div>
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center mb-2">
                    <span>مراقبة الأمان:</span>
                    <span id="security-monitoring" class="badge bg-secondary">معطل</span>
                </div>
                <div class="d-flex justify-content-between align-items-center mb-2">
                    <span>تسجيل الفيديو:</span>
                    <span id="video-recording" class="badge bg-secondary">معطل</span>
                </div>
                <div class="d-flex justify-content-between align-items-center mb-2">
                    <span>كشف التلاعب:</span>
                    <span id="spoofing-detection" class="badge bg-secondary">معطل</span>
                </div>
                <div class="d-flex justify-content-between align-items-center">
                    <span>التنبيهات اليوم:</span>
                    <span id="today-alerts" class="badge bg-warning">0</span>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Recent Activity -->
<div class="card">
    <div class="card-header">
        <h5 class="mb-0"><i class="bi bi-clock-history me-2"></i>النشاط الأخير</h5>
    </div>
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-hover table-custom">
                <thead class="table-dark">
                    <tr>
                        <th>الوقت</th>
                        <th>الشخص</th>
                        <th>الثقة</th>
                        <th>الكاميرا</th>
                        <th>الحالة</th>
                    </tr>
                </thead>
                <tbody id="recent-activity-table">
                    <tr>
                        <td colspan="5" class="text-center text-muted">لا توجد أنشطة حديثة</td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    let performanceChart, recognitionChart;
    let performanceData = [];
    let recognitionData = { known: 0, unknown: 0 };
    
    // Initialize charts
    document.addEventListener('DOMContentLoaded', function() {
        initializeCharts();
        loadDashboardData();
        
        // Set up event listeners
        document.getElementById('start-system-btn').addEventListener('click', startSystem);
        document.getElementById('stop-system-btn').addEventListener('click', stopSystem);
    });
    
    function initializeCharts() {
        // Performance Chart
        const performanceCtx = document.getElementById('performance-chart').getContext('2d');
        performanceChart = new Chart(performanceCtx, {
            type: 'line',
            data: {
                labels: [],
                datasets: [{
                    label: 'FPS',
                    data: [],
                    borderColor: '#667eea',
                    backgroundColor: 'rgba(102, 126, 234, 0.1)',
                    tension: 0.4
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: true,
                        max: 60
                    }
                },
                plugins: {
                    legend: {
                        display: false
                    }
                }
            }
        });
        
        // Recognition Chart
        const recognitionCtx = document.getElementById('recognition-chart').getContext('2d');
        recognitionChart = new Chart(recognitionCtx, {
            type: 'doughnut',
            data: {
                labels: ['وجوه معروفة', 'وجوه غير معروفة'],
                datasets: [{
                    data: [0, 0],
                    backgroundColor: ['#28a745', '#dc3545'],
                    borderWidth: 0
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom'
                    }
                }
            }
        });
    }
    
    async function loadDashboardData() {
        try {
            // Load statistics
            const stats = await apiCall('/api/statistics');
            updateStatistics(stats);
            
            // Load recent activity
            const recentActivity = await apiCall('/api/recognition-log?days=1');
            updateRecentActivity(recentActivity);
            
        } catch (error) {
            console.error('Failed to load dashboard data:', error);
        }
    }
    
    function updateDashboard(liveData) {
        if (liveData.system_status) {
            // Update metrics
            document.getElementById('fps-metric').textContent = 
                Math.round(liveData.system_status.fps || 0);
            document.getElementById('faces-detected-metric').textContent = 
                liveData.system_status.faces_detected || 0;
            
            // Update performance chart
            updatePerformanceChart(liveData.system_status.fps || 0);
            
            // Update system status indicators
            const isRunning = liveData.system_status.running;
            document.getElementById('camera-status').textContent = isRunning ? 'نشط' : 'معطل';
            document.getElementById('camera-status').className = 
                `badge ${isRunning ? 'bg-success' : 'bg-danger'}`;
        }
    }
    
    function updatePerformanceChart(fps) {
        const now = new Date().toLocaleTimeString('ar-SA');
        
        performanceChart.data.labels.push(now);
        performanceChart.data.datasets[0].data.push(fps);
        
        // Keep only last 20 data points
        if (performanceChart.data.labels.length > 20) {
            performanceChart.data.labels.shift();
            performanceChart.data.datasets[0].data.shift();
        }
        
        performanceChart.update('none');
    }
    
    function updateStatistics(stats) {
        if (stats.performance) {
            document.getElementById('faces-recognized-metric').textContent = 
                formatNumber(stats.performance.total_faces_recognized || 0);
            document.getElementById('uptime-metric').textContent = 
                stats.performance.session_duration || '00:00:00';
        }
        
        if (stats.security) {
            document.getElementById('security-monitoring').textContent = 
                stats.security.monitoring_active ? 'نشط' : 'معطل';
            document.getElementById('security-monitoring').className = 
                `badge ${stats.security.monitoring_active ? 'bg-success' : 'bg-secondary'}`;
            
            document.getElementById('video-recording').textContent = 
                stats.security.is_recording ? 'نشط' : 'معطل';
            document.getElementById('video-recording').className = 
                `badge ${stats.security.is_recording ? 'bg-success' : 'bg-secondary'}`;
            
            document.getElementById('today-alerts').textContent = 
                formatNumber(stats.security.total_alerts || 0);
        }
        
        if (stats.database) {
            document.getElementById('registered-persons').textContent = 
                formatNumber(stats.database.persons_count || 0);
        }
    }
    
    function updateRecentActivity(activities) {
        const tbody = document.getElementById('recent-activity-table');
        
        if (!activities || activities.length === 0) {
            tbody.innerHTML = '<tr><td colspan="5" class="text-center text-muted">لا توجد أنشطة حديثة</td></tr>';
            return;
        }
        
        tbody.innerHTML = activities.slice(0, 10).map(activity => `
            <tr>
                <td>${formatDateTime(activity.timestamp)}</td>
                <td>${activity.person_name}</td>
                <td>
                    <span class="badge ${activity.confidence > 80 ? 'bg-success' : activity.confidence > 60 ? 'bg-warning' : 'bg-danger'}">
                        ${Math.round(activity.confidence)}%
                    </span>
                </td>
                <td>كاميرا ${activity.camera_id}</td>
                <td>
                    <span class="badge bg-success">
                        <i class="bi bi-check-circle"></i> تم التعرف
                    </span>
                </td>
            </tr>
        `).join('');
    }
    
    async function startSystem() {
        try {
            const result = await apiCall('/api/system/start', { method: 'POST' });
            showAlert(result.message, 'success');
        } catch (error) {
            showAlert('فشل في بدء تشغيل النظام', 'danger');
        }
    }
    
    async function stopSystem() {
        try {
            const result = await apiCall('/api/system/stop', { method: 'POST' });
            showAlert(result.message, 'warning');
        } catch (error) {
            showAlert('فشل في إيقاف النظام', 'danger');
        }
    }
    
    // Auto-refresh dashboard data every 30 seconds
    setInterval(loadDashboardData, 30000);
</script>
{% endblock %}
