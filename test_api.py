#!/usr/bin/env python3
"""
اختبار APIs النظام
"""

import requests
import json

def test_persons_api():
    """اختبار API الأشخاص"""
    try:
        print("🧪 اختبار API الأشخاص...")
        
        response = requests.get("http://localhost:5000/api/persons")
        print(f"📊 كود الاستجابة: {response.status_code}")
        print(f"📄 رؤوس الاستجابة: {dict(response.headers)}")
        
        if response.status_code == 200:
            try:
                data = response.json()
                print(f"✅ تم تحميل {len(data)} شخص بنجاح")
                
                for i, person in enumerate(data[:3]):  # أول 3 أشخاص
                    print(f"   {i+1}. {person.get('name', 'غير معروف')} - {person.get('email', 'لا يوجد إيميل')}")
                    
            except json.JSONDecodeError as e:
                print(f"❌ خطأ في تحليل JSON: {e}")
                print(f"📄 محتوى الاستجابة: {response.text[:500]}")
        else:
            print(f"❌ فشل في الطلب: {response.status_code}")
            print(f"📄 محتوى الخطأ: {response.text}")
            
    except requests.exceptions.ConnectionError:
        print("❌ لا يمكن الاتصال بالخادم. تأكد من تشغيل الخادم على http://localhost:5000")
    except Exception as e:
        print(f"❌ خطأ غير متوقع: {e}")

def test_add_person():
    """اختبار إضافة شخص جديد"""
    try:
        print("\n🧪 اختبار إضافة شخص جديد...")
        
        # بيانات الشخص الجديد
        person_data = {
            "name": "Test User",
            "full_name": "Test User Full Name",
            "email": "<EMAIL>",
            "phone": "123456789",
            "department": "IT",
            "position": "Developer",
            "notes": "شخص تجريبي"
        }
        
        response = requests.post(
            "http://localhost:5000/api/persons",
            json=person_data,
            headers={"Content-Type": "application/json"}
        )
        
        print(f"📊 كود الاستجابة: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ تم إضافة الشخص بنجاح: {result.get('message', 'لا توجد رسالة')}")
            print(f"🆔 معرف الشخص: {result.get('id', 'غير محدد')}")
        else:
            print(f"❌ فشل في إضافة الشخص: {response.status_code}")
            try:
                error = response.json()
                print(f"📄 رسالة الخطأ: {error.get('error', 'خطأ غير محدد')}")
            except:
                print(f"📄 محتوى الخطأ: {response.text}")
                
    except Exception as e:
        print(f"❌ خطأ في اختبار إضافة الشخص: {e}")

def test_other_apis():
    """اختبار APIs أخرى"""
    apis = [
        ("حالة النظام", "/api/status"),
        ("الإحصائيات", "/api/statistics"),
        ("الإعدادات", "/api/settings"),
        ("سجل التعرف", "/api/recognition-log?days=1")
    ]
    
    print("\n🧪 اختبار APIs أخرى...")
    
    for name, endpoint in apis:
        try:
            response = requests.get(f"http://localhost:5000{endpoint}")
            if response.status_code == 200:
                print(f"✅ {name}: يعمل")
            else:
                print(f"❌ {name}: فشل ({response.status_code})")
        except Exception as e:
            print(f"❌ {name}: خطأ - {e}")

if __name__ == "__main__":
    print("🎯 اختبار شامل لـ APIs النظام")
    print("=" * 50)
    
    test_persons_api()
    test_add_person()
    test_other_apis()
    
    print("\n" + "=" * 50)
    print("✅ انتهى الاختبار")
