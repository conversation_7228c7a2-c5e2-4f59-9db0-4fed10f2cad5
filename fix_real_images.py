#!/usr/bin/env python3
"""
ربط الصور الحقيقية بأصحابها
"""

import sys
import os
import glob

# إضافة مسار المشروع إلى Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from src.database.database_manager import DatabaseManager
from src.config.settings import Settings

def find_real_images():
    """البحث عن الصور الحقيقية في مجلد الأشخاص"""
    persons_dir = os.path.join(os.getcwd(), 'data', 'persons')
    
    # البحث عن جميع الصور
    image_extensions = ['*.jpg', '*.jpeg', '*.png', '*.bmp', '*.gif']
    all_images = []
    
    for ext in image_extensions:
        all_images.extend(glob.glob(os.path.join(persons_dir, ext)))
    
    # فلترة الصور الحقيقية (استبعاد الصور الافتراضية)
    real_images = []
    for img_path in all_images:
        filename = os.path.basename(img_path)
        if '_default.' not in filename:  # استبعاد الصور الافتراضية
            real_images.append(img_path)
    
    return real_images

def match_images_to_persons():
    """ربط الصور بالأشخاص"""
    try:
        settings = Settings()
        db = DatabaseManager(settings.database.database_path)
        persons = db.get_all_persons()
        
        # البحث عن الصور الحقيقية
        real_images = find_real_images()
        
        print(f"عدد الأشخاص: {len(persons)}")
        print(f"عدد الصور الحقيقية: {len(real_images)}")
        print("-" * 50)
        
        # عرض الصور الموجودة
        print("الصور الحقيقية الموجودة:")
        for img_path in real_images:
            filename = os.path.basename(img_path)
            print(f"  - {filename}")
        print("-" * 50)
        
        updated_count = 0
        
        for person in persons:
            name = person.get('name', '').lower()
            person_id = person.get('id')
            current_image = person.get('image_path', '')
            
            print(f"\nالبحث عن صورة لـ: {person.get('name')}")
            print(f"الصورة الحالية: {current_image}")
            
            # البحث عن صورة تحتوي على اسم الشخص
            matching_image = None
            for img_path in real_images:
                filename = os.path.basename(img_path).lower()
                
                # البحث بطرق مختلفة
                if (name in filename or 
                    filename.startswith(name) or
                    name.replace(' ', '') in filename.replace(' ', '') or
                    name.replace(' ', '_') in filename or
                    name.replace(' ', '-') in filename):
                    
                    matching_image = img_path
                    break
            
            if matching_image:
                # التحقق من أن الصورة ليست افتراضية
                if '_default.' not in matching_image:
                    print(f"✅ تم العثور على صورة: {os.path.basename(matching_image)}")
                    
                    # تحديث قاعدة البيانات
                    db.update_person(person_id, image_path=matching_image)
                    updated_count += 1
                    
                    # نسخ الصورة إلى مجلد static أيضاً
                    import shutil
                    static_dir = os.path.join(os.getcwd(), 'src', 'web', 'static', 'persons')
                    os.makedirs(static_dir, exist_ok=True)
                    
                    dest_path = os.path.join(static_dir, os.path.basename(matching_image))
                    shutil.copy2(matching_image, dest_path)
                    print(f"📁 تم نسخ الصورة إلى: {dest_path}")
                else:
                    print(f"⏭️ تم تخطي الصورة الافتراضية")
            else:
                print(f"❌ لم يتم العثور على صورة حقيقية")
        
        print("-" * 50)
        print(f"✅ تم تحديث {updated_count} شخص بصور حقيقية")
        
        # عرض النتائج النهائية
        print("\nالنتائج النهائية:")
        persons = db.get_all_persons()  # إعادة تحميل البيانات
        for person in persons:
            name = person.get('name')
            image_path = person.get('image_path', '')
            filename = os.path.basename(image_path) if image_path else 'لا توجد صورة'
            
            if image_path and '_default.' not in image_path:
                print(f"✅ {name}: {filename} (صورة حقيقية)")
            elif image_path and '_default.' in image_path:
                print(f"🎨 {name}: {filename} (صورة افتراضية)")
            else:
                print(f"❌ {name}: لا توجد صورة")
                
    except Exception as e:
        print(f"خطأ: {e}")

if __name__ == "__main__":
    match_images_to_persons()
