{% extends "base.html" %}

{% block title %}إدارة الأشخاص - نظام التعرف على الوجوه{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h2><i class="bi bi-people me-2"></i>إدارة الأشخاص (نسخة مبسطة)</h2>
    <div>
        <button class="btn btn-primary" onclick="loadPersons()">
            <i class="bi bi-arrow-clockwise"></i> تحديث القائمة
        </button>
    </div>
</div>

<!-- Persons Table -->
<div class="card">
    <div class="card-header">
        <h5 class="mb-0"><i class="bi bi-table me-2"></i>قائمة الأشخاص</h5>
    </div>
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-hover table-custom">
                <thead class="table-dark">
                    <tr>
                        <th>ID</th>
                        <th>الاسم</th>
                        <th>الاسم الكامل</th>
                        <th>البريد الإلكتروني</th>
                        <th>الهاتف</th>
                        <th>القسم</th>
                        <th>تاريخ الإضافة</th>
                    </tr>
                </thead>
                <tbody id="persons-table">
                    <tr>
                        <td colspan="7" class="text-center">
                            <div class="loading-spinner spinner-border text-primary" role="status">
                                <span class="visually-hidden">جاري التحميل...</span>
                            </div>
                            جاري تحميل البيانات...
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>
</div>

<div class="mt-4">
    <h4>معلومات التشخيص</h4>
    <div id="debug-info" class="alert alert-info">
        <div>حالة التحميل: <span id="loading-status">لم يبدأ</span></div>
        <div>عدد الأشخاص: <span id="persons-count">0</span></div>
        <div>آخر خطأ: <span id="last-error">لا يوجد</span></div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    let personsData = [];
    
    function updateStatus(status) {
        document.getElementById('loading-status').textContent = status;
        console.log('📊 حالة التحميل:', status);
    }
    
    function updateCount(count) {
        document.getElementById('persons-count').textContent = count;
    }
    
    function updateError(error) {
        document.getElementById('last-error').textContent = error;
    }
    
    async function loadPersons() {
        const tbody = document.getElementById('persons-table');
        
        try {
            updateStatus('جاري التحميل...');
            updateError('لا يوجد');
            
            console.log('🔄 بدء تحميل الأشخاص...');
            
            // عرض مؤشر التحميل
            tbody.innerHTML = `
                <tr>
                    <td colspan="7" class="text-center">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">جاري التحميل...</span>
                        </div>
                        <div class="mt-2">جاري تحميل البيانات...</div>
                    </td>
                </tr>
            `;
            
            const response = await fetch('/api/persons');
            console.log('📡 استجابة الخادم:', response.status, response.statusText);
            
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }
            
            personsData = await response.json();
            console.log('✅ تم تحميل البيانات:', personsData.length, 'شخص');
            console.log('📋 البيانات:', personsData);
            
            if (!Array.isArray(personsData)) {
                throw new Error('البيانات المستلمة ليست مصفوفة');
            }
            
            updateStatus('تم التحميل بنجاح');
            updateCount(personsData.length);
            
            // عرض البيانات في الجدول
            if (personsData.length === 0) {
                tbody.innerHTML = '<tr><td colspan="7" class="text-center text-muted">لا توجد أشخاص مسجلون</td></tr>';
            } else {
                tbody.innerHTML = personsData.map(person => `
                    <tr>
                        <td>${person.id}</td>
                        <td><strong>${person.name}</strong></td>
                        <td>${person.full_name || '-'}</td>
                        <td>${person.email || '-'}</td>
                        <td>${person.phone || '-'}</td>
                        <td>${person.department || '-'}</td>
                        <td>${formatDateTime(person.created_at)}</td>
                    </tr>
                `).join('');
            }
            
        } catch (error) {
            console.error('❌ خطأ في تحميل الأشخاص:', error);
            updateStatus('فشل في التحميل');
            updateError(error.message);
            
            tbody.innerHTML = `
                <tr>
                    <td colspan="7" class="text-center text-danger">
                        <i class="bi bi-exclamation-triangle me-2"></i>
                        فشل في تحميل البيانات: ${error.message}
                        <br>
                        <button class="btn btn-sm btn-outline-primary mt-2" onclick="loadPersons()">
                            <i class="bi bi-arrow-clockwise"></i> إعادة المحاولة
                        </button>
                    </td>
                </tr>
            `;
        }
    }
    
    function formatDateTime(dateString) {
        if (!dateString) return '-';
        try {
            const date = new Date(dateString);
            return date.toLocaleDateString('ar-SA') + ' ' + date.toLocaleTimeString('ar-SA', {hour: '2-digit', minute: '2-digit'});
        } catch (e) {
            return dateString;
        }
    }
    
    // تحميل تلقائي عند تحميل الصفحة
    document.addEventListener('DOMContentLoaded', function() {
        console.log('🚀 صفحة الأشخاص المبسطة تم تحميلها');
        
        // التحقق من وجود العناصر المطلوبة
        const personsTable = document.getElementById('persons-table');
        if (!personsTable) {
            console.error('❌ لم يتم العثور على جدول الأشخاص!');
            updateError('لم يتم العثور على جدول الأشخاص');
            return;
        }
        
        console.log('✅ تم العثور على جدول الأشخاص');
        
        // تحميل الأشخاص بعد ثانية واحدة
        setTimeout(() => {
            console.log('⏰ بدء التحميل التلقائي...');
            loadPersons();
        }, 1000);
    });
</script>
{% endblock %}
