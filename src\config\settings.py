"""
نظام إدارة الإعدادات المتقدم
Advanced Settings Management System
"""

import json
import os
from typing import Dict, Any, Optional
from dataclasses import dataclass, asdict
from pathlib import Path


@dataclass
class CameraSettings:
    """إعدادات الكاميرا"""
    camera_index: int = 0
    resolution_width: int = 1280
    resolution_height: int = 720
    fps: int = 30
    brightness: float = 0.5
    contrast: float = 0.5


@dataclass
class RecognitionSettings:
    """إعدادات التعرف على الوجوه"""
    confidence_threshold: float = 0.6
    face_detection_model: str = "hog"  # hog أو cnn
    face_encoding_model: str = "large"  # small أو large
    max_faces_per_frame: int = 10
    recognition_tolerance: float = 0.6
    frame_skip: int = 2  # معالجة كل إطار ثاني


@dataclass
class UISettings:
    """إعدادات واجهة المستخدم"""
    window_width: int = 1024
    window_height: int = 768
    show_fps: bool = True
    show_confidence: bool = True
    show_statistics: bool = True
    font_scale: float = 0.8
    font_thickness: int = 2
    box_thickness: int = 2
    language: str = "ar"  # ar أو en


@dataclass
class SecuritySettings:
    """إعدادات الأمان"""
    enable_recording: bool = False
    recording_path: str = "data/videos"
    enable_alerts: bool = False
    alert_unknown_faces: bool = True
    max_unknown_alerts_per_minute: int = 5
    enable_face_spoofing_detection: bool = False


@dataclass
class DatabaseSettings:
    """إعدادات قاعدة البيانات"""
    database_path: str = "data/face_recognition.db"
    backup_interval_hours: int = 24
    max_log_entries: int = 10000
    enable_statistics: bool = True


@dataclass
class PerformanceSettings:
    """إعدادات الأداء"""
    enable_multithreading: bool = True
    max_worker_threads: int = 4
    image_resize_factor: float = 0.25
    enable_gpu_acceleration: bool = False
    memory_optimization: bool = True
    cache_encodings: bool = True


class Settings:
    """فئة إدارة الإعدادات الرئيسية"""
    
    def __init__(self, config_file: str = "config/settings.json"):
        self.config_file = Path(config_file)
        self.camera = CameraSettings()
        self.recognition = RecognitionSettings()
        self.ui = UISettings()
        self.security = SecuritySettings()
        self.database = DatabaseSettings()
        self.performance = PerformanceSettings()
        
        # إنشاء مجلد الإعدادات إذا لم يكن موجوداً
        self.config_file.parent.mkdir(parents=True, exist_ok=True)
        
        # تحميل الإعدادات
        self.load()
    
    def load(self) -> None:
        """تحميل الإعدادات من الملف"""
        if self.config_file.exists():
            try:
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                
                # تحديث الإعدادات من البيانات المحملة
                if 'camera' in data:
                    self.camera = CameraSettings(**data['camera'])
                if 'recognition' in data:
                    self.recognition = RecognitionSettings(**data['recognition'])
                if 'ui' in data:
                    self.ui = UISettings(**data['ui'])
                if 'security' in data:
                    self.security = SecuritySettings(**data['security'])
                if 'database' in data:
                    self.database = DatabaseSettings(**data['database'])
                if 'performance' in data:
                    self.performance = PerformanceSettings(**data['performance'])
                    
            except Exception as e:
                print(f"خطأ في تحميل الإعدادات: {e}")
                print("سيتم استخدام الإعدادات الافتراضية")
    
    def save(self) -> None:
        """حفظ الإعدادات في الملف"""
        try:
            data = {
                'camera': asdict(self.camera),
                'recognition': asdict(self.recognition),
                'ui': asdict(self.ui),
                'security': asdict(self.security),
                'database': asdict(self.database),
                'performance': asdict(self.performance)
            }
            
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, indent=4, ensure_ascii=False)
                
        except Exception as e:
            print(f"خطأ في حفظ الإعدادات: {e}")
    
    def reset_to_defaults(self) -> None:
        """إعادة تعيين الإعدادات للقيم الافتراضية"""
        self.camera = CameraSettings()
        self.recognition = RecognitionSettings()
        self.ui = UISettings()
        self.security = SecuritySettings()
        self.database = DatabaseSettings()
        self.performance = PerformanceSettings()
        self.save()
    
    def get_persons_path(self) -> str:
        """الحصول على مسار مجلد الأشخاص"""
        return "data/persons"
    
    def get_encodings_path(self) -> str:
        """الحصول على مسار ملف الترميزات"""
        return "data/encodings/face_encodings.pkl"
    
    def get_logs_path(self) -> str:
        """الحصول على مسار ملفات السجلات"""
        return "data/logs"
    
    def validate_settings(self) -> bool:
        """التحقق من صحة الإعدادات"""
        try:
            # التحقق من إعدادات الكاميرا
            if self.camera.camera_index < 0:
                return False
            if self.camera.fps <= 0:
                return False
                
            # التحقق من إعدادات التعرف
            if not (0.0 <= self.recognition.confidence_threshold <= 1.0):
                return False
            if not (0.0 <= self.recognition.recognition_tolerance <= 1.0):
                return False
                
            # التحقق من إعدادات الأداء
            if self.performance.max_worker_threads <= 0:
                return False
            if not (0.1 <= self.performance.image_resize_factor <= 1.0):
                return False
                
            return True
            
        except Exception:
            return False
    
    def __str__(self) -> str:
        """تمثيل نصي للإعدادات"""
        return f"""
إعدادات النظام:
- الكاميرا: فهرس {self.camera.camera_index}، دقة {self.camera.resolution_width}x{self.camera.resolution_height}
- التعرف: عتبة الثقة {self.recognition.confidence_threshold}، نموذج {self.recognition.face_detection_model}
- الواجهة: حجم النافذة {self.ui.window_width}x{self.ui.window_height}، اللغة {self.ui.language}
- الأمان: تسجيل {'مفعل' if self.security.enable_recording else 'معطل'}، تنبيهات {'مفعلة' if self.security.enable_alerts else 'معطلة'}
- الأداء: خيوط متعددة {'مفعل' if self.performance.enable_multithreading else 'معطل'}، عامل التصغير {self.performance.image_resize_factor}
        """
