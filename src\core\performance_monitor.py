"""
مراقب الأداء
Performance Monitor
"""

import time
import psutil
import threading
from collections import deque
from typing import Dict, List, Optional
from dataclasses import dataclass
from datetime import datetime, timedelta

from ..utils.logger import get_logger


@dataclass
class PerformanceMetrics:
    """مقاييس الأداء"""
    fps: float = 0.0
    processing_time: float = 0.0
    cpu_usage: float = 0.0
    memory_usage: float = 0.0
    gpu_usage: float = 0.0
    faces_detected: int = 0
    faces_recognized: int = 0
    timestamp: datetime = None


class PerformanceMonitor:
    """مراقب الأداء المتقدم"""
    
    def __init__(self, history_size: int = 100):
        self.logger = get_logger()
        self.history_size = history_size
        
        # تاريخ المقاييس
        self.metrics_history = deque(maxlen=history_size)
        self.fps_history = deque(maxlen=30)  # آخر 30 إطار
        
        # متغيرات التوقيت
        self.frame_start_time = None
        self.last_fps_time = time.time()
        self.frame_count = 0
        
        # إحصائيات الجلسة
        self.session_start_time = datetime.now()
        self.total_frames_processed = 0
        self.total_faces_detected = 0
        self.total_faces_recognized = 0
        
        # خيط مراقبة النظام
        self.monitoring_thread = None
        self.is_monitoring = False
        
        self.logger.info("تم تهيئة مراقب الأداء")
    
    def start_monitoring(self):
        """بدء مراقبة النظام"""
        if not self.is_monitoring:
            self.is_monitoring = True
            self.monitoring_thread = threading.Thread(target=self._monitor_system, daemon=True)
            self.monitoring_thread.start()
            self.logger.info("تم بدء مراقبة الأداء")
    
    def stop_monitoring(self):
        """إيقاف مراقبة النظام"""
        self.is_monitoring = False
        if self.monitoring_thread:
            self.monitoring_thread.join(timeout=1.0)
        self.logger.info("تم إيقاف مراقبة الأداء")
    
    def start_frame_processing(self):
        """بدء معالجة إطار"""
        self.frame_start_time = time.time()
    
    def end_frame_processing(self, faces_detected: int = 0, faces_recognized: int = 0):
        """انتهاء معالجة إطار"""
        if self.frame_start_time is None:
            return
        
        # حساب وقت المعالجة
        processing_time = time.time() - self.frame_start_time
        
        # تحديث العدادات
        self.frame_count += 1
        self.total_frames_processed += 1
        self.total_faces_detected += faces_detected
        self.total_faces_recognized += faces_recognized
        
        # حساب FPS
        current_time = time.time()
        time_diff = current_time - self.last_fps_time
        
        if time_diff >= 1.0:  # تحديث FPS كل ثانية
            fps = self.frame_count / time_diff
            self.fps_history.append(fps)
            
            # إعادة تعيين العدادات
            self.frame_count = 0
            self.last_fps_time = current_time
            
            # إنشاء مقاييس الأداء
            metrics = PerformanceMetrics(
                fps=fps,
                processing_time=processing_time,
                faces_detected=faces_detected,
                faces_recognized=faces_recognized,
                timestamp=datetime.now()
            )
            
            # إضافة مقاييس النظام
            self._add_system_metrics(metrics)
            
            # حفظ في التاريخ
            self.metrics_history.append(metrics)
            
            # تسجيل المقاييس
            self.logger.log_performance_metrics(fps, processing_time, faces_detected)
    
    def _add_system_metrics(self, metrics: PerformanceMetrics):
        """إضافة مقاييس النظام"""
        try:
            # استخدام CPU
            metrics.cpu_usage = psutil.cpu_percent(interval=None)
            
            # استخدام الذاكرة
            memory = psutil.virtual_memory()
            metrics.memory_usage = memory.percent
            
            # محاولة الحصول على استخدام GPU (إذا كان متاحاً)
            try:
                import GPUtil
                gpus = GPUtil.getGPUs()
                if gpus:
                    metrics.gpu_usage = gpus[0].load * 100
            except ImportError:
                metrics.gpu_usage = 0.0
                
        except Exception as e:
            self.logger.error(f"خطأ في جمع مقاييس النظام: {e}")
    
    def _monitor_system(self):
        """مراقبة النظام في الخلفية"""
        while self.is_monitoring:
            try:
                # فحص استخدام الموارد
                cpu_percent = psutil.cpu_percent(interval=1.0)
                memory_percent = psutil.virtual_memory().percent
                
                # تحذيرات الأداء
                if cpu_percent > 90:
                    self.logger.warning(f"استخدام CPU مرتفع: {cpu_percent:.1f}%")
                
                if memory_percent > 90:
                    self.logger.warning(f"استخدام الذاكرة مرتفع: {memory_percent:.1f}%")
                
                # فحص درجة الحرارة (إذا كان متاحاً)
                try:
                    temps = psutil.sensors_temperatures()
                    if temps:
                        for name, entries in temps.items():
                            for entry in entries:
                                if entry.current > 80:  # درجة حرارة عالية
                                    self.logger.warning(f"درجة حرارة مرتفعة {name}: {entry.current}°C")
                except:
                    pass  # تجاهل إذا لم تكن متاحة
                
                time.sleep(5)  # فحص كل 5 ثوان
                
            except Exception as e:
                self.logger.error(f"خطأ في مراقبة النظام: {e}")
                time.sleep(1)
    
    def get_current_fps(self) -> float:
        """الحصول على FPS الحالي"""
        return self.fps_history[-1] if self.fps_history else 0.0
    
    def get_average_fps(self) -> float:
        """الحصول على متوسط FPS"""
        if not self.fps_history:
            return 0.0
        return sum(self.fps_history) / len(self.fps_history)
    
    def get_latest_metrics(self) -> Optional[PerformanceMetrics]:
        """الحصول على أحدث مقاييس"""
        return self.metrics_history[-1] if self.metrics_history else None
    
    def get_session_statistics(self) -> Dict:
        """الحصول على إحصائيات الجلسة"""
        session_duration = datetime.now() - self.session_start_time
        
        return {
            'session_duration': str(session_duration).split('.')[0],  # بدون الميكروثواني
            'total_frames_processed': self.total_frames_processed,
            'total_faces_detected': self.total_faces_detected,
            'total_faces_recognized': self.total_faces_recognized,
            'average_fps': self.get_average_fps(),
            'current_fps': self.get_current_fps(),
            'recognition_rate': (self.total_faces_recognized / max(self.total_faces_detected, 1)) * 100
        }
    
    def get_performance_report(self) -> Dict:
        """تقرير شامل عن الأداء"""
        if not self.metrics_history:
            return {}
        
        # حساب المتوسطات
        avg_fps = sum(m.fps for m in self.metrics_history) / len(self.metrics_history)
        avg_processing_time = sum(m.processing_time for m in self.metrics_history) / len(self.metrics_history)
        avg_cpu = sum(m.cpu_usage for m in self.metrics_history) / len(self.metrics_history)
        avg_memory = sum(m.memory_usage for m in self.metrics_history) / len(self.metrics_history)
        
        # أقصى وأدنى قيم
        max_fps = max(m.fps for m in self.metrics_history)
        min_fps = min(m.fps for m in self.metrics_history)
        max_processing_time = max(m.processing_time for m in self.metrics_history)
        min_processing_time = min(m.processing_time for m in self.metrics_history)
        
        return {
            'averages': {
                'fps': round(avg_fps, 2),
                'processing_time': round(avg_processing_time * 1000, 2),  # بالميلي ثانية
                'cpu_usage': round(avg_cpu, 2),
                'memory_usage': round(avg_memory, 2)
            },
            'extremes': {
                'max_fps': round(max_fps, 2),
                'min_fps': round(min_fps, 2),
                'max_processing_time': round(max_processing_time * 1000, 2),
                'min_processing_time': round(min_processing_time * 1000, 2)
            },
            'session': self.get_session_statistics()
        }
    
    def log_performance_summary(self):
        """تسجيل ملخص الأداء"""
        report = self.get_performance_report()
        if report:
            self.logger.info("=== ملخص الأداء ===")
            self.logger.info(f"متوسط FPS: {report['averages']['fps']}")
            self.logger.info(f"متوسط وقت المعالجة: {report['averages']['processing_time']} ms")
            self.logger.info(f"متوسط استخدام CPU: {report['averages']['cpu_usage']}%")
            self.logger.info(f"متوسط استخدام الذاكرة: {report['averages']['memory_usage']}%")
            self.logger.info(f"إجمالي الإطارات المعالجة: {report['session']['total_frames_processed']}")
            self.logger.info(f"إجمالي الوجوه المكتشفة: {report['session']['total_faces_detected']}")
            self.logger.info(f"معدل التعرف: {report['session']['recognition_rate']:.1f}%")
    
    def reset_statistics(self):
        """إعادة تعيين الإحصائيات"""
        self.metrics_history.clear()
        self.fps_history.clear()
        self.session_start_time = datetime.now()
        self.total_frames_processed = 0
        self.total_faces_detected = 0
        self.total_faces_recognized = 0
        self.frame_count = 0
        self.last_fps_time = time.time()
        
        self.logger.info("تم إعادة تعيين إحصائيات الأداء")

    def stop_monitoring(self):
        """إيقاف مراقبة الأداء"""
        self.monitoring_active = False
        self.logger.info("تم إيقاف مراقبة الأداء")
