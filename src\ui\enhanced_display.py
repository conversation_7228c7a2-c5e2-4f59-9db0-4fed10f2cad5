"""
واجهة العرض المحسنة
Enhanced Display Interface
"""

import cv2
import numpy as np
from datetime import datetime
from typing import Dict, List, Tuple, Optional
from pathlib import Path

from ..config.settings import Settings
from ..utils.logger import get_logger


class EnhancedDisplay:
    """واجهة عرض محسنة مع عناصر تفاعلية"""
    
    def __init__(self, settings: Settings):
        self.settings = settings
        self.logger = get_logger()
        
        # ألوان النظام
        self.colors = {
            'primary': (0, 255, 0),      # أخضر للوجوه المعروفة
            'secondary': (0, 0, 255),    # أحمر للوجوه غير المعروفة
            'accent': (255, 255, 0),     # أصفر للمعلومات
            'background': (0, 0, 0),     # أسود للخلفية
            'text': (255, 255, 255),     # أبيض للنص
            'warning': (0, 165, 255),    # برتقالي للتحذيرات
            'success': (0, 255, 0),      # أخضر للنجاح
            'error': (0, 0, 255)         # أحمر للأخطاء
        }
        
        # خطوط النظام
        self.fonts = {
            'title': cv2.FONT_HERSHEY_TRIPLEX,
            'subtitle': cv2.FONT_HERSHEY_DUPLEX,
            'body': cv2.FONT_HERSHEY_SIMPLEX,
            'mono': cv2.FONT_HERSHEY_COMPLEX_SMALL
        }
        
        # حالة الواجهة
        self.show_menu = False
        self.show_stats_panel = True
        self.show_help = False
        self.current_mode = "normal"  # normal, debug, performance
        
        # إحصائيات العرض
        self.display_stats = {
            'total_frames': 0,
            'faces_detected': 0,
            'faces_recognized': 0,
            'unknown_faces': 0,
            'session_start': datetime.now()
        }
        
        self.logger.info("تم تهيئة واجهة العرض المحسنة")
    
    def draw_enhanced_frame(self, frame: np.ndarray, face_data: List[Dict], 
                           performance_data: Dict) -> np.ndarray:
        """رسم إطار محسن مع جميع العناصر"""
        enhanced_frame = frame.copy()
        
        # رسم الوجوه
        for face_info in face_data:
            self._draw_enhanced_face_box(enhanced_frame, face_info)
        
        # رسم لوحة المعلومات
        if self.show_stats_panel:
            self._draw_stats_panel(enhanced_frame, performance_data)
        
        # رسم شريط الحالة
        self._draw_status_bar(enhanced_frame, performance_data)
        
        # رسم القائمة إذا كانت مفعلة
        if self.show_menu:
            self._draw_menu(enhanced_frame)
        
        # رسم المساعدة إذا كانت مفعلة
        if self.show_help:
            self._draw_help_overlay(enhanced_frame)
        
        # رسم معلومات التصحيح في وضع التصحيح
        if self.current_mode == "debug":
            self._draw_debug_info(enhanced_frame, face_data)
        
        # تحديث الإحصائيات
        self._update_display_stats(face_data)
        
        return enhanced_frame
    
    def _draw_enhanced_face_box(self, frame: np.ndarray, face_info: Dict):
        """رسم مربع وجه محسن"""
        location = face_info['location']
        name = face_info.get('name', 'غير معروف')
        confidence = face_info.get('confidence', 0)
        is_known = face_info.get('is_known', False)
        
        top, right, bottom, left = location
        
        # اختيار اللون حسب الحالة
        if is_known:
            if confidence > 80:
                color = self.colors['success']
            elif confidence > 60:
                color = self.colors['primary']
            else:
                color = self.colors['warning']
        else:
            color = self.colors['secondary']
        
        # رسم مربع متدرج
        self._draw_gradient_box(frame, (left, top), (right, bottom), color)
        
        # رسم زوايا مميزة
        self._draw_corner_markers(frame, (left, top), (right, bottom), color)
        
        # رسم شريط المعلومات
        self._draw_info_bar(frame, (left, bottom), (right, bottom + 40), 
                           name, confidence, color)
        
        # رسم مؤشر الثقة
        if is_known:
            self._draw_confidence_indicator(frame, (right + 5, top), confidence)
    
    def _draw_gradient_box(self, frame: np.ndarray, pt1: Tuple[int, int], 
                          pt2: Tuple[int, int], color: Tuple[int, int, int]):
        """رسم مربع متدرج"""
        # رسم الحدود الخارجية
        cv2.rectangle(frame, pt1, pt2, color, 3)
        
        # رسم حدود داخلية شفافة
        overlay = frame.copy()
        cv2.rectangle(overlay, 
                     (pt1[0] + 2, pt1[1] + 2), 
                     (pt2[0] - 2, pt2[1] - 2), 
                     color, 1)
        cv2.addWeighted(frame, 0.8, overlay, 0.2, 0, frame)
    
    def _draw_corner_markers(self, frame: np.ndarray, pt1: Tuple[int, int], 
                           pt2: Tuple[int, int], color: Tuple[int, int, int]):
        """رسم علامات الزوايا"""
        x1, y1 = pt1
        x2, y2 = pt2
        length = 20
        thickness = 4
        
        # الزاوية العلوية اليسرى
        cv2.line(frame, (x1, y1), (x1 + length, y1), color, thickness)
        cv2.line(frame, (x1, y1), (x1, y1 + length), color, thickness)
        
        # الزاوية العلوية اليمنى
        cv2.line(frame, (x2, y1), (x2 - length, y1), color, thickness)
        cv2.line(frame, (x2, y1), (x2, y1 + length), color, thickness)
        
        # الزاوية السفلية اليسرى
        cv2.line(frame, (x1, y2), (x1 + length, y2), color, thickness)
        cv2.line(frame, (x1, y2), (x1, y2 - length), color, thickness)
        
        # الزاوية السفلية اليمنى
        cv2.line(frame, (x2, y2), (x2 - length, y2), color, thickness)
        cv2.line(frame, (x2, y2), (x2, y2 - length), color, thickness)
    
    def _draw_info_bar(self, frame: np.ndarray, pt1: Tuple[int, int], 
                      pt2: Tuple[int, int], name: str, confidence: float,
                      color: Tuple[int, int, int]):
        """رسم شريط المعلومات"""
        # رسم خلفية شفافة
        overlay = frame.copy()
        cv2.rectangle(overlay, pt1, pt2, color, cv2.FILLED)
        cv2.addWeighted(frame, 0.7, overlay, 0.3, 0, frame)
        
        # رسم النص
        if confidence > 0:
            text = f"{name} ({confidence:.1f}%)"
        else:
            text = name
        
        # حساب موقع النص
        text_size = cv2.getTextSize(text, self.fonts['body'], 0.7, 2)[0]
        text_x = pt1[0] + 5
        text_y = pt1[1] + (pt2[1] - pt1[1] + text_size[1]) // 2
        
        # رسم النص مع حدود
        cv2.putText(frame, text, (text_x, text_y), 
                   self.fonts['body'], 0.7, (0, 0, 0), 3)  # حدود سوداء
        cv2.putText(frame, text, (text_x, text_y), 
                   self.fonts['body'], 0.7, self.colors['text'], 2)  # نص أبيض
    
    def _draw_confidence_indicator(self, frame: np.ndarray, position: Tuple[int, int], 
                                 confidence: float):
        """رسم مؤشر الثقة"""
        x, y = position
        bar_width = 8
        bar_height = 60
        
        # رسم خلفية المؤشر
        cv2.rectangle(frame, (x, y), (x + bar_width, y + bar_height), 
                     (50, 50, 50), cv2.FILLED)
        
        # حساب ارتفاع المؤشر
        fill_height = int((confidence / 100) * bar_height)
        
        # اختيار لون المؤشر
        if confidence > 80:
            indicator_color = self.colors['success']
        elif confidence > 60:
            indicator_color = self.colors['warning']
        else:
            indicator_color = self.colors['error']
        
        # رسم المؤشر
        cv2.rectangle(frame, (x, y + bar_height - fill_height), 
                     (x + bar_width, y + bar_height), 
                     indicator_color, cv2.FILLED)
        
        # رسم النسبة المئوية
        cv2.putText(frame, f"{confidence:.0f}%", (x - 10, y + bar_height + 15), 
                   self.fonts['mono'], 0.4, self.colors['text'], 1)
    
    def _draw_stats_panel(self, frame: np.ndarray, performance_data: Dict):
        """رسم لوحة الإحصائيات"""
        height, width = frame.shape[:2]
        panel_width = 250
        panel_height = 200
        panel_x = width - panel_width - 10
        panel_y = 10
        
        # رسم خلفية اللوحة
        overlay = frame.copy()
        cv2.rectangle(overlay, (panel_x, panel_y), 
                     (panel_x + panel_width, panel_y + panel_height), 
                     (0, 0, 0), cv2.FILLED)
        cv2.addWeighted(frame, 0.7, overlay, 0.3, 0, frame)
        
        # رسم حدود اللوحة
        cv2.rectangle(frame, (panel_x, panel_y), 
                     (panel_x + panel_width, panel_y + panel_height), 
                     self.colors['accent'], 2)
        
        # رسم العنوان
        cv2.putText(frame, "إحصائيات النظام", (panel_x + 10, panel_y + 25), 
                   self.fonts['subtitle'], 0.6, self.colors['accent'], 2)
        
        # رسم الإحصائيات
        y_offset = panel_y + 50
        line_height = 20
        
        stats_text = [
            f"FPS: {performance_data.get('fps', 0):.1f}",
            f"الإطارات: {self.display_stats['total_frames']}",
            f"الوجوه المكتشفة: {self.display_stats['faces_detected']}",
            f"الوجوه المعروفة: {self.display_stats['faces_recognized']}",
            f"الوجوه غير المعروفة: {self.display_stats['unknown_faces']}",
            f"وقت التشغيل: {self._get_session_duration()}",
            f"استخدام CPU: {performance_data.get('cpu_usage', 0):.1f}%",
            f"استخدام الذاكرة: {performance_data.get('memory_usage', 0):.1f}%"
        ]
        
        for i, text in enumerate(stats_text):
            cv2.putText(frame, text, (panel_x + 10, y_offset + i * line_height), 
                       self.fonts['mono'], 0.5, self.colors['text'], 1)
    
    def _draw_status_bar(self, frame: np.ndarray, performance_data: Dict):
        """رسم شريط الحالة"""
        height, width = frame.shape[:2]
        bar_height = 30
        bar_y = height - bar_height
        
        # رسم خلفية الشريط
        overlay = frame.copy()
        cv2.rectangle(overlay, (0, bar_y), (width, height), (0, 0, 0), cv2.FILLED)
        cv2.addWeighted(frame, 0.8, overlay, 0.2, 0, frame)
        
        # رسم معلومات الحالة
        current_time = datetime.now().strftime("%H:%M:%S")
        status_text = f"الوقت: {current_time} | الوضع: {self.current_mode} | اضغط H للمساعدة"
        
        cv2.putText(frame, status_text, (10, bar_y + 20), 
                   self.fonts['mono'], 0.5, self.colors['text'], 1)
        
        # رسم مؤشر FPS
        fps = performance_data.get('fps', 0)
        fps_color = self.colors['success'] if fps > 20 else self.colors['warning'] if fps > 10 else self.colors['error']
        cv2.putText(frame, f"FPS: {fps:.1f}", (width - 100, bar_y + 20), 
                   self.fonts['mono'], 0.5, fps_color, 1)

    def _draw_menu(self, frame: np.ndarray):
        """رسم القائمة الرئيسية"""
        height, width = frame.shape[:2]
        menu_width = 300
        menu_height = 400
        menu_x = (width - menu_width) // 2
        menu_y = (height - menu_height) // 2

        # رسم خلفية القائمة
        overlay = frame.copy()
        cv2.rectangle(overlay, (menu_x, menu_y),
                     (menu_x + menu_width, menu_y + menu_height),
                     (0, 0, 0), cv2.FILLED)
        cv2.addWeighted(frame, 0.5, overlay, 0.5, 0, frame)

        # رسم حدود القائمة
        cv2.rectangle(frame, (menu_x, menu_y),
                     (menu_x + menu_width, menu_y + menu_height),
                     self.colors['accent'], 3)

        # رسم عنوان القائمة
        cv2.putText(frame, "قائمة التحكم", (menu_x + 20, menu_y + 40),
                   self.fonts['title'], 0.8, self.colors['accent'], 2)

        # رسم خيارات القائمة
        menu_items = [
            "M - إخفاء/إظهار القائمة",
            "S - إخفاء/إظهار الإحصائيات",
            "D - وضع التصحيح",
            "P - وضع الأداء",
            "R - إعادة تعيين الإحصائيات",
            "C - التقاط لقطة شاشة",
            "H - المساعدة",
            "Q/ESC - الخروج"
        ]

        y_offset = menu_y + 80
        for i, item in enumerate(menu_items):
            cv2.putText(frame, item, (menu_x + 20, y_offset + i * 30),
                       self.fonts['body'], 0.6, self.colors['text'], 1)

    def _draw_help_overlay(self, frame: np.ndarray):
        """رسم تراكب المساعدة"""
        height, width = frame.shape[:2]
        help_width = 500
        help_height = 350
        help_x = (width - help_width) // 2
        help_y = (height - help_height) // 2

        # رسم خلفية المساعدة
        overlay = frame.copy()
        cv2.rectangle(overlay, (help_x, help_y),
                     (help_x + help_width, help_y + help_height),
                     (0, 0, 0), cv2.FILLED)
        cv2.addWeighted(frame, 0.3, overlay, 0.7, 0, frame)

        # رسم حدود المساعدة
        cv2.rectangle(frame, (help_x, help_y),
                     (help_x + help_width, help_y + help_height),
                     self.colors['primary'], 3)

        # رسم عنوان المساعدة
        cv2.putText(frame, "دليل الاستخدام", (help_x + 20, help_y + 40),
                   self.fonts['title'], 0.8, self.colors['primary'], 2)

        # رسم نص المساعدة
        help_text = [
            "مرحباً بك في نظام التعرف على الوجوه الاحترافي",
            "",
            "اختصارات لوحة المفاتيح:",
            "• Q أو ESC: الخروج من البرنامج",
            "• M: إظهار/إخفاء قائمة التحكم",
            "• S: إظهار/إخفاء لوحة الإحصائيات",
            "• D: تبديل وضع التصحيح",
            "• P: تبديل وضع مراقبة الأداء",
            "• R: إعادة تعيين الإحصائيات",
            "• C: التقاط لقطة شاشة",
            "• H: إظهار/إخفاء هذه المساعدة",
            "",
            "الألوان:",
            "• أخضر: وجه معروف (ثقة عالية)",
            "• أصفر: وجه معروف (ثقة متوسطة)",
            "• أحمر: وجه غير معروف"
        ]

        y_offset = help_y + 70
        for i, line in enumerate(help_text):
            if line.startswith("•"):
                color = self.colors['accent']
                font = self.fonts['mono']
                scale = 0.5
            elif line == "":
                continue
            else:
                color = self.colors['text']
                font = self.fonts['body']
                scale = 0.6

            cv2.putText(frame, line, (help_x + 20, y_offset + i * 18),
                       font, scale, color, 1)

    def _draw_debug_info(self, frame: np.ndarray, face_data: List[Dict]):
        """رسم معلومات التصحيح"""
        height, width = frame.shape[:2]
        debug_y = 50

        # رسم معلومات الإطار
        debug_info = [
            f"حجم الإطار: {width}x{height}",
            f"عدد الوجوه: {len(face_data)}",
            f"الوضع الحالي: {self.current_mode}",
            f"الذاكرة المستخدمة: {self._get_memory_usage():.1f} MB"
        ]

        for i, info in enumerate(debug_info):
            cv2.putText(frame, info, (10, debug_y + i * 25),
                       self.fonts['mono'], 0.6, self.colors['warning'], 2)

        # رسم معلومات تفصيلية لكل وجه
        for i, face_info in enumerate(face_data):
            location = face_info['location']
            top, right, bottom, left = location

            # رسم إحداثيات الوجه
            coord_text = f"({left},{top})-({right},{bottom})"
            cv2.putText(frame, coord_text, (left, top - 10),
                       self.fonts['mono'], 0.4, self.colors['warning'], 1)

    def _update_display_stats(self, face_data: List[Dict]):
        """تحديث إحصائيات العرض"""
        self.display_stats['total_frames'] += 1
        self.display_stats['faces_detected'] += len(face_data)

        for face_info in face_data:
            if face_info.get('is_known', False):
                self.display_stats['faces_recognized'] += 1
            else:
                self.display_stats['unknown_faces'] += 1

    def _get_session_duration(self) -> str:
        """الحصول على مدة الجلسة"""
        duration = datetime.now() - self.display_stats['session_start']
        hours, remainder = divmod(duration.total_seconds(), 3600)
        minutes, seconds = divmod(remainder, 60)
        return f"{int(hours):02d}:{int(minutes):02d}:{int(seconds):02d}"

    def _get_memory_usage(self) -> float:
        """الحصول على استخدام الذاكرة بالميجابايت"""
        try:
            import psutil
            process = psutil.Process()
            return process.memory_info().rss / 1024 / 1024
        except:
            return 0.0

    def handle_key_press(self, key: int) -> str:
        """معالجة ضغطات المفاتيح"""
        if key == ord('m') or key == ord('M'):
            self.show_menu = not self.show_menu
            return "menu_toggled"

        elif key == ord('s') or key == ord('S'):
            self.show_stats_panel = not self.show_stats_panel
            return "stats_toggled"

        elif key == ord('h') or key == ord('H'):
            self.show_help = not self.show_help
            return "help_toggled"

        elif key == ord('d') or key == ord('D'):
            self.current_mode = "debug" if self.current_mode != "debug" else "normal"
            return "debug_toggled"

        elif key == ord('p') or key == ord('P'):
            self.current_mode = "performance" if self.current_mode != "performance" else "normal"
            return "performance_toggled"

        elif key == ord('r') or key == ord('R'):
            self.reset_stats()
            return "stats_reset"

        elif key == ord('c') or key == ord('C'):
            return "screenshot"

        elif key == ord('q') or key == ord('Q') or key == 27:  # ESC
            return "quit"

        return "unknown"

    def reset_stats(self):
        """إعادة تعيين الإحصائيات"""
        self.display_stats = {
            'total_frames': 0,
            'faces_detected': 0,
            'faces_recognized': 0,
            'unknown_faces': 0,
            'session_start': datetime.now()
        }
        self.logger.info("تم إعادة تعيين إحصائيات العرض")

    def get_display_info(self) -> Dict:
        """الحصول على معلومات العرض"""
        return {
            'show_menu': self.show_menu,
            'show_stats_panel': self.show_stats_panel,
            'show_help': self.show_help,
            'current_mode': self.current_mode,
            'stats': self.display_stats.copy()
        }
