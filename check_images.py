#!/usr/bin/env python3
"""
فحص مسارات الصور الشخصية
"""

import sys
import os

# إضافة مسار المشروع إلى Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from src.database.database_manager import DatabaseManager
from src.config.settings import Settings

def main():
    """فحص مسارات الصور"""
    try:
        settings = Settings()
        db = DatabaseManager(settings.database.database_path)
        persons = db.get_all_persons()
        
        print(f"عدد الأشخاص: {len(persons)}")
        print("\nالأشخاص ومسارات صورهم:")
        print("-" * 50)
        
        for person in persons:
            name = person.get('name', 'غير محدد')
            image_path = person.get('image_path', None)
            
            print(f"الاسم: {name}")
            print(f"مسار الصورة: {image_path}")
            
            if image_path:
                # التحقق من وجود الملف
                if os.path.exists(image_path):
                    print(f"✅ الملف موجود")
                else:
                    print(f"❌ الملف غير موجود")
                    
                # استخراج اسم الملف
                filename = os.path.basename(image_path)
                print(f"اسم الملف: {filename}")
            else:
                print("❌ لا توجد صورة")
                
            print("-" * 30)
            
    except Exception as e:
        print(f"خطأ: {e}")

if __name__ == "__main__":
    main()
