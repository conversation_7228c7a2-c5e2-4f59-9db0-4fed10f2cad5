<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار ترتيب الأزرار الجديد</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: white;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        
        .sidebar {
            background: linear-gradient(180deg, #2c3e50 0%, #34495e 100%);
            min-height: 100vh;
            box-shadow: 2px 0 10px rgba(0, 0, 0, 0.1);
        }
        
        .nav-link {
            color: rgba(255, 255, 255, 0.8);
            padding: 12px 20px;
            margin: 5px 0;
            border-radius: 8px;
            transition: all 0.3s ease;
            text-decoration: none;
            display: flex;
            align-items: center;
        }
        
        .nav-link:hover {
            background: rgba(255, 255, 255, 0.1);
            color: white;
            transform: translateX(5px);
        }
        
        .admin-link {
            background: linear-gradient(135deg, rgba(220, 53, 69, 0.2), rgba(220, 53, 69, 0.1)) !important;
            border-left: 4px solid #dc3545 !important;
            font-weight: bold !important;
            order: -10 !important;
        }
        
        .dashboard-link {
            order: -5 !important;
        }
        
        .nav.flex-column {
            display: flex !important;
            flex-direction: column !important;
        }
        
        .test-info {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 10px;
            padding: 20px;
            margin: 20px;
            backdrop-filter: blur(10px);
        }
        
        .order-indicator {
            background: #28a745;
            color: white;
            border-radius: 50%;
            width: 25px;
            height: 25px;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            font-weight: bold;
            margin-left: 10px;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- القائمة الجانبية مع الترتيب الجديد -->
            <div class="col-md-3 sidebar p-0">
                <div class="p-3">
                    <!-- عنوان القائمة -->
                    <div class="text-center mb-4">
                        <h5 class="text-white">القائمة الرئيسية</h5>
                        <small class="text-muted">الترتيب الجديد</small>
                        <hr class="text-white-50">
                    </div>
                    
                    <!-- القائمة مع الترتيب الجديد -->
                    <nav class="nav flex-column">
                        <a class="nav-link admin-link" href="/admin">
                            <span class="order-indicator">1</span>
                            <i class="bi bi-shield-check me-2"></i>
                            لوحة التحكم الإدارية
                        </a>
                        <a class="nav-link dashboard-link" href="/dashboard">
                            <span class="order-indicator">2</span>
                            <i class="bi bi-speedometer2 me-2"></i>
                            لوحة التحكم العامة
                        </a>
                        <a class="nav-link" href="/persons">
                            <span class="order-indicator">3</span>
                            <i class="bi bi-people me-2"></i>
                            إدارة الأشخاص
                        </a>
                        <a class="nav-link" href="/recognition">
                            <span class="order-indicator">4</span>
                            <i class="bi bi-camera-video me-2"></i>
                            التعرف على الوجوه
                        </a>
                        <a class="nav-link" href="/camera-test">
                            <span class="order-indicator">5</span>
                            <i class="bi bi-camera me-2"></i>
                            تشخيص الكاميرا
                        </a>
                        <a class="nav-link" href="/logs">
                            <span class="order-indicator">6</span>
                            <i class="bi bi-journal-text me-2"></i>
                            السجلات
                        </a>
                        <a class="nav-link" href="/settings">
                            <span class="order-indicator">7</span>
                            <i class="bi bi-gear me-2"></i>
                            الإعدادات
                        </a>
                    </nav>
                </div>
            </div>
            
            <!-- المحتوى الرئيسي -->
            <div class="col-md-9">
                <div class="p-4">
                    <h1 class="text-center mb-4">🔄 اختبار الترتيب الجديد للأزرار</h1>
                    
                    <div class="test-info">
                        <h3>✅ تم تبديل ترتيب الأزرار بنجاح:</h3>
                        <div class="row">
                            <div class="col-md-6">
                                <h5>الترتيب السابق:</h5>
                                <ol>
                                    <li>📊 لوحة التحكم العامة</li>
                                    <li>🛡️ لوحة التحكم الإدارية</li>
                                </ol>
                            </div>
                            <div class="col-md-6">
                                <h5>الترتيب الجديد:</h5>
                                <ol>
                                    <li><strong>🛡️ لوحة التحكم الإدارية</strong></li>
                                    <li>📊 لوحة التحكم العامة</li>
                                </ol>
                            </div>
                        </div>
                    </div>
                    
                    <div class="test-info">
                        <h3>🎯 الميزات الجديدة:</h3>
                        <ul>
                            <li>✅ الزر الإداري في المقدمة (المركز الأول)</li>
                            <li>✅ تصميم أحمر مميز للزر الإداري</li>
                            <li>✅ أرقام ترتيب واضحة</li>
                            <li>✅ CSS Flexbox Order لضمان الترتيب</li>
                            <li>✅ JavaScript لإجبار إعادة الترتيب</li>
                        </ul>
                    </div>
                    
                    <div class="test-info">
                        <h3>🔧 التقنيات المستخدمة:</h3>
                        <ul>
                            <li><code>CSS order: -10</code> للزر الإداري</li>
                            <li><code>CSS order: -5</code> للوحة التحكم العامة</li>
                            <li><code>JavaScript forceReorder()</code> لإجبار الترتيب</li>
                            <li><code>setInterval()</code> للتحقق الدوري</li>
                        </ul>
                    </div>
                    
                    <div class="text-center mt-4">
                        <a href="/admin" class="btn btn-danger btn-lg me-3">
                            <i class="bi bi-shield-check me-2"></i>
                            لوحة التحكم الإدارية (الأول)
                        </a>
                        <a href="/dashboard" class="btn btn-primary btn-lg">
                            <i class="bi bi-speedometer2 me-2"></i>
                            لوحة التحكم العامة (الثاني)
                        </a>
                    </div>
                    
                    <div class="test-info mt-4">
                        <h3>📋 التعليمات:</h3>
                        <ol>
                            <li>تحقق من القائمة الجانبية</li>
                            <li>يجب أن ترى "لوحة التحكم الإدارية" كأول زر</li>
                            <li>يجب أن ترى "لوحة التحكم العامة" كثاني زر</li>
                            <li>الأرقام تُظهر الترتيب الصحيح</li>
                        </ol>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <script>
        console.log('🧪 صفحة اختبار الترتيب الجديد');
        
        document.addEventListener('DOMContentLoaded', function() {
            // التحقق من الترتيب
            const nav = document.querySelector('nav.nav.flex-column');
            const links = nav.querySelectorAll('a');
            
            console.log('📋 ترتيب الأزرار:');
            links.forEach((link, index) => {
                const href = link.getAttribute('href');
                const text = link.textContent.trim();
                console.log(`${index + 1}. ${href} - ${text}`);
            });
            
            // التحقق من أن الزر الإداري في المقدمة
            const firstLink = links[0];
            if (firstLink && firstLink.getAttribute('href') === '/admin') {
                console.log('✅ الزر الإداري في المقدمة بنجاح!');
                firstLink.style.boxShadow = '0 0 20px rgba(40, 167, 69, 0.5)';
                setTimeout(() => {
                    firstLink.style.boxShadow = '';
                }, 3000);
            } else {
                console.log('❌ الزر الإداري ليس في المقدمة');
            }
        });
    </script>
</body>
</html>
