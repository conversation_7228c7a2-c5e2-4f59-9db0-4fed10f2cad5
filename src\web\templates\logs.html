{% extends "base.html" %}

{% block title %}السجلات - نظام التعرف على الوجوه{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h2><i class="bi bi-journal-text me-2"></i>السجلات والتقارير</h2>
    <div>
        <button class="btn btn-primary" onclick="exportLogs()">
            <i class="bi bi-download"></i> تصدير السجلات
        </button>
        <button class="btn btn-warning" onclick="clearOldLogs()">
            <i class="bi bi-trash"></i> مسح السجلات القديمة
        </button>
    </div>
</div>

<!-- Filter Controls -->
<div class="card mb-4">
    <div class="card-body">
        <div class="row">
            <div class="col-md-3">
                <label for="date-from" class="form-label">من تاريخ</label>
                <input type="date" class="form-control" id="date-from">
            </div>
            <div class="col-md-3">
                <label for="date-to" class="form-label">إلى تاريخ</label>
                <input type="date" class="form-control" id="date-to">
            </div>
            <div class="col-md-3">
                <label for="person-filter" class="form-label">الشخص</label>
                <select class="form-select" id="person-filter">
                    <option value="">جميع الأشخاص</option>
                </select>
            </div>
            <div class="col-md-3">
                <label for="confidence-filter" class="form-label">مستوى الثقة</label>
                <select class="form-select" id="confidence-filter">
                    <option value="">جميع المستويات</option>
                    <option value="high">عالي (80%+)</option>
                    <option value="medium">متوسط (60-80%)</option>
                    <option value="low">منخفض (أقل من 60%)</option>
                </select>
            </div>
        </div>
        <div class="row mt-3">
            <div class="col-md-12">
                <button class="btn btn-primary" onclick="applyFilters()">
                    <i class="bi bi-funnel"></i> تطبيق الفلاتر
                </button>
                <button class="btn btn-secondary" onclick="resetFilters()">
                    <i class="bi bi-arrow-clockwise"></i> إعادة تعيين
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Statistics Cards -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="metric-card">
            <div class="metric-value" id="total-recognitions">0</div>
            <div class="metric-label">إجمالي التعرفات</div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="metric-card">
            <div class="metric-value" id="unique-persons">0</div>
            <div class="metric-label">أشخاص مختلفون</div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="metric-card">
            <div class="metric-value" id="avg-confidence">0%</div>
            <div class="metric-label">متوسط الثقة</div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="metric-card">
            <div class="metric-value" id="today-count">0</div>
            <div class="metric-label">تعرفات اليوم</div>
        </div>
    </div>
</div>

<!-- Recognition Logs Table -->
<div class="card mb-4">
    <div class="card-header">
        <h5 class="mb-0"><i class="bi bi-list-ul me-2"></i>سجل التعرف على الوجوه</h5>
    </div>
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-hover table-custom">
                <thead class="table-dark">
                    <tr>
                        <th>التاريخ والوقت</th>
                        <th>الشخص</th>
                        <th>مستوى الثقة</th>
                        <th>الكاميرا</th>
                        <th>الموقع</th>
                        <th>الإجراءات</th>
                    </tr>
                </thead>
                <tbody id="recognition-logs-table">
                    <tr>
                        <td colspan="6" class="text-center">
                            <div class="loading-spinner spinner-border text-primary" role="status">
                                <span class="visually-hidden">جاري التحميل...</span>
                            </div>
                            جاري تحميل السجلات...
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>
        
        <!-- Pagination -->
        <nav aria-label="صفحات السجلات">
            <ul class="pagination justify-content-center" id="logs-pagination">
                <!-- سيتم إنشاؤها ديناميكياً -->
            </ul>
        </nav>
    </div>
</div>

<!-- System Events -->
<div class="card mb-4">
    <div class="card-header">
        <h5 class="mb-0"><i class="bi bi-exclamation-triangle me-2"></i>أحداث النظام</h5>
    </div>
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-hover">
                <thead class="table-light">
                    <tr>
                        <th>الوقت</th>
                        <th>نوع الحدث</th>
                        <th>الوصف</th>
                        <th>الخطورة</th>
                    </tr>
                </thead>
                <tbody id="system-events-table">
                    <tr>
                        <td colspan="4" class="text-center text-muted">جاري تحميل أحداث النظام...</td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>
</div>

<!-- Security Report -->
<div class="card">
    <div class="card-header">
        <h5 class="mb-0"><i class="bi bi-shield-check me-2"></i>تقرير الأمان</h5>
    </div>
    <div class="card-body">
        <div class="row">
            <div class="col-md-6">
                <h6>إحصائيات الأمان</h6>
                <ul class="list-unstyled">
                    <li><strong>إجمالي التنبيهات:</strong> <span id="total-alerts">0</span></li>
                    <li><strong>الوجوه غير المعروفة:</strong> <span id="unknown-faces">0</span></li>
                    <li><strong>محاولات التلاعب:</strong> <span id="spoofing-attempts">0</span></li>
                    <li><strong>التسجيلات المحفوظة:</strong> <span id="recordings-count">0</span></li>
                </ul>
            </div>
            <div class="col-md-6">
                <h6>حالة المراقبة</h6>
                <ul class="list-unstyled">
                    <li><strong>مراقبة الأمان:</strong> <span id="security-monitoring-status" class="badge bg-secondary">غير معروف</span></li>
                    <li><strong>تسجيل الفيديو:</strong> <span id="recording-status" class="badge bg-secondary">غير معروف</span></li>
                    <li><strong>كشف التلاعب:</strong> <span id="spoofing-detection-status" class="badge bg-secondary">غير معروف</span></li>
                    <li><strong>آخر تحديث:</strong> <span id="last-update">غير معروف</span></li>
                </ul>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    let currentPage = 1;
    let logsPerPage = 20;
    let allLogs = [];
    let filteredLogs = [];
    
    document.addEventListener('DOMContentLoaded', function() {
        // تعيين التواريخ الافتراضية
        const today = new Date();
        const weekAgo = new Date(today.getTime() - 7 * 24 * 60 * 60 * 1000);
        
        document.getElementById('date-from').value = weekAgo.toISOString().split('T')[0];
        document.getElementById('date-to').value = today.toISOString().split('T')[0];
        
        loadLogs();
        loadSystemEvents();
        loadSecurityReport();
        loadPersonsFilter();
    });
    
    async function loadLogs() {
        try {
            const days = 30; // آخر 30 يوم
            allLogs = await apiCall(`/api/recognition-log?days=${days}`);
            filteredLogs = [...allLogs];
            
            updateStatistics();
            renderLogsTable();
            
        } catch (error) {
            console.error('Failed to load logs:', error);
            document.getElementById('recognition-logs-table').innerHTML = 
                '<tr><td colspan="6" class="text-center text-danger">فشل في تحميل السجلات</td></tr>';
        }
    }
    
    async function loadSystemEvents() {
        try {
            // هذا API غير موجود حالياً، سنضيفه لاحقاً
            const events = []; // await apiCall('/api/system-events');
            renderSystemEvents(events);
        } catch (error) {
            document.getElementById('system-events-table').innerHTML = 
                '<tr><td colspan="4" class="text-center text-muted">لا توجد أحداث نظام</td></tr>';
        }
    }
    
    async function loadSecurityReport() {
        try {
            const report = await apiCall('/api/security/report');
            updateSecurityReport(report);
        } catch (error) {
            console.error('Failed to load security report:', error);
        }
    }
    
    async function loadPersonsFilter() {
        try {
            const persons = await apiCall('/api/persons');
            const select = document.getElementById('person-filter');
            
            select.innerHTML = '<option value="">جميع الأشخاص</option>' +
                persons.map(person => `<option value="${person.name}">${person.name}</option>`).join('');
        } catch (error) {
            console.error('Failed to load persons for filter:', error);
        }
    }
    
    function updateStatistics() {
        const totalRecognitions = filteredLogs.length;
        const uniquePersons = new Set(filteredLogs.map(log => log.person_name)).size;
        const avgConfidence = filteredLogs.length > 0 ? 
            filteredLogs.reduce((sum, log) => sum + log.confidence, 0) / filteredLogs.length : 0;
        
        const today = new Date().toISOString().split('T')[0];
        const todayLogs = filteredLogs.filter(log => log.timestamp.startsWith(today));
        
        document.getElementById('total-recognitions').textContent = formatNumber(totalRecognitions);
        document.getElementById('unique-persons').textContent = formatNumber(uniquePersons);
        document.getElementById('avg-confidence').textContent = Math.round(avgConfidence) + '%';
        document.getElementById('today-count').textContent = formatNumber(todayLogs.length);
    }
    
    function renderLogsTable() {
        const tbody = document.getElementById('recognition-logs-table');
        
        if (filteredLogs.length === 0) {
            tbody.innerHTML = '<tr><td colspan="6" class="text-center text-muted">لا توجد سجلات</td></tr>';
            return;
        }
        
        // تطبيق الصفحات
        const startIndex = (currentPage - 1) * logsPerPage;
        const endIndex = startIndex + logsPerPage;
        const pageData = filteredLogs.slice(startIndex, endIndex);
        
        tbody.innerHTML = pageData.map(log => `
            <tr>
                <td>${formatDateTime(log.timestamp)}</td>
                <td><strong>${log.person_name}</strong></td>
                <td>
                    <span class="badge ${getConfidenceBadgeClass(log.confidence)}">
                        ${Math.round(log.confidence)}%
                    </span>
                </td>
                <td>كاميرا ${log.camera_id}</td>
                <td>${log.location_data || 'غير محدد'}</td>
                <td>
                    <button class="btn btn-sm btn-outline-primary" onclick="viewLogDetails(${log.id})" title="عرض التفاصيل">
                        <i class="bi bi-eye"></i>
                    </button>
                </td>
            </tr>
        `).join('');
        
        renderPagination();
    }
    
    function renderPagination() {
        const totalPages = Math.ceil(filteredLogs.length / logsPerPage);
        const pagination = document.getElementById('logs-pagination');
        
        if (totalPages <= 1) {
            pagination.innerHTML = '';
            return;
        }
        
        let paginationHTML = '';
        
        // Previous button
        paginationHTML += `
            <li class="page-item ${currentPage === 1 ? 'disabled' : ''}">
                <a class="page-link" href="#" onclick="changePage(${currentPage - 1})">السابق</a>
            </li>
        `;
        
        // Page numbers
        for (let i = 1; i <= totalPages; i++) {
            if (i === currentPage || i === 1 || i === totalPages || (i >= currentPage - 1 && i <= currentPage + 1)) {
                paginationHTML += `
                    <li class="page-item ${i === currentPage ? 'active' : ''}">
                        <a class="page-link" href="#" onclick="changePage(${i})">${i}</a>
                    </li>
                `;
            } else if (i === currentPage - 2 || i === currentPage + 2) {
                paginationHTML += '<li class="page-item disabled"><span class="page-link">...</span></li>';
            }
        }
        
        // Next button
        paginationHTML += `
            <li class="page-item ${currentPage === totalPages ? 'disabled' : ''}">
                <a class="page-link" href="#" onclick="changePage(${currentPage + 1})">التالي</a>
            </li>
        `;
        
        pagination.innerHTML = paginationHTML;
    }
    
    function changePage(page) {
        const totalPages = Math.ceil(filteredLogs.length / logsPerPage);
        if (page >= 1 && page <= totalPages) {
            currentPage = page;
            renderLogsTable();
        }
    }
    
    function getConfidenceBadgeClass(confidence) {
        if (confidence >= 80) return 'bg-success';
        if (confidence >= 60) return 'bg-warning';
        return 'bg-danger';
    }
    
    function applyFilters() {
        const dateFrom = document.getElementById('date-from').value;
        const dateTo = document.getElementById('date-to').value;
        const personFilter = document.getElementById('person-filter').value;
        const confidenceFilter = document.getElementById('confidence-filter').value;
        
        filteredLogs = allLogs.filter(log => {
            const logDate = log.timestamp.split('T')[0];
            
            // فلتر التاريخ
            if (dateFrom && logDate < dateFrom) return false;
            if (dateTo && logDate > dateTo) return false;
            
            // فلتر الشخص
            if (personFilter && log.person_name !== personFilter) return false;
            
            // فلتر مستوى الثقة
            if (confidenceFilter) {
                if (confidenceFilter === 'high' && log.confidence < 80) return false;
                if (confidenceFilter === 'medium' && (log.confidence < 60 || log.confidence >= 80)) return false;
                if (confidenceFilter === 'low' && log.confidence >= 60) return false;
            }
            
            return true;
        });
        
        currentPage = 1;
        updateStatistics();
        renderLogsTable();
        
        showAlert('تم تطبيق الفلاتر بنجاح', 'success');
    }
    
    function resetFilters() {
        const today = new Date();
        const weekAgo = new Date(today.getTime() - 7 * 24 * 60 * 60 * 1000);
        
        document.getElementById('date-from').value = weekAgo.toISOString().split('T')[0];
        document.getElementById('date-to').value = today.toISOString().split('T')[0];
        document.getElementById('person-filter').value = '';
        document.getElementById('confidence-filter').value = '';
        
        filteredLogs = [...allLogs];
        currentPage = 1;
        updateStatistics();
        renderLogsTable();
        
        showAlert('تم إعادة تعيين الفلاتر', 'info');
    }
    
    function renderSystemEvents(events) {
        const tbody = document.getElementById('system-events-table');
        
        if (!events || events.length === 0) {
            tbody.innerHTML = '<tr><td colspan="4" class="text-center text-muted">لا توجد أحداث نظام</td></tr>';
            return;
        }
        
        tbody.innerHTML = events.slice(0, 10).map(event => `
            <tr>
                <td>${formatDateTime(event.timestamp)}</td>
                <td>${event.event_type}</td>
                <td>${event.event_data || event.description}</td>
                <td>
                    <span class="badge ${getSeverityBadgeClass(event.severity)}">
                        ${event.severity}
                    </span>
                </td>
            </tr>
        `).join('');
    }
    
    function getSeverityBadgeClass(severity) {
        switch (severity) {
            case 'INFO': return 'bg-info';
            case 'WARNING': return 'bg-warning';
            case 'ERROR': return 'bg-danger';
            case 'CRITICAL': return 'bg-dark';
            default: return 'bg-secondary';
        }
    }
    
    function updateSecurityReport(report) {
        if (report && report.session_stats) {
            document.getElementById('total-alerts').textContent = formatNumber(report.session_stats.total_alerts || 0);
            document.getElementById('unknown-faces').textContent = formatNumber(report.session_stats.unknown_faces_detected || 0);
            document.getElementById('spoofing-attempts').textContent = formatNumber(report.session_stats.spoofing_attempts || 0);
            document.getElementById('recordings-count').textContent = formatNumber(report.session_stats.recordings_created || 0);
            
            // حالة المراقبة
            const isMonitoring = report.system_status?.monitoring_active;
            document.getElementById('security-monitoring-status').textContent = isMonitoring ? 'نشط' : 'معطل';
            document.getElementById('security-monitoring-status').className = 
                `badge ${isMonitoring ? 'bg-success' : 'bg-secondary'}`;
            
            const isRecording = report.session_stats?.is_recording;
            document.getElementById('recording-status').textContent = isRecording ? 'نشط' : 'معطل';
            document.getElementById('recording-status').className = 
                `badge ${isRecording ? 'bg-success' : 'bg-secondary'}`;
            
            const spoofingEnabled = report.system_status?.spoofing_detection_enabled;
            document.getElementById('spoofing-detection-status').textContent = spoofingEnabled ? 'مفعل' : 'معطل';
            document.getElementById('spoofing-detection-status').className = 
                `badge ${spoofingEnabled ? 'bg-success' : 'bg-secondary'}`;
            
            document.getElementById('last-update').textContent = formatDateTime(report.report_generated);
        }
    }
    
    function viewLogDetails(logId) {
        const log = allLogs.find(l => l.id === logId);
        if (log) {
            alert(`تفاصيل السجل:\n\nالشخص: ${log.person_name}\nالثقة: ${log.confidence}%\nالوقت: ${formatDateTime(log.timestamp)}\nالكاميرا: ${log.camera_id}`);
        }
    }
    
    function exportLogs() {
        // تصدير السجلات كـ CSV
        const csvContent = "data:text/csv;charset=utf-8," + 
            "التاريخ والوقت,الشخص,مستوى الثقة,الكاميرا\n" +
            filteredLogs.map(log => 
                `"${log.timestamp}","${log.person_name}","${log.confidence}","${log.camera_id}"`
            ).join("\n");
        
        const encodedUri = encodeURI(csvContent);
        const link = document.createElement("a");
        link.setAttribute("href", encodedUri);
        link.setAttribute("download", `face_recognition_logs_${new Date().toISOString().split('T')[0]}.csv`);
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        
        showAlert('تم تصدير السجلات بنجاح', 'success');
    }
    
    function clearOldLogs() {
        if (confirm('هل أنت متأكد من حذف السجلات القديمة؟ هذا الإجراء لا يمكن التراجع عنه.')) {
            // هنا يمكن إضافة API call لحذف السجلات القديمة
            showAlert('تم حذف السجلات القديمة', 'warning');
        }
    }
    
    // تحديث تلقائي كل دقيقة
    setInterval(() => {
        loadLogs();
        loadSecurityReport();
    }, 60000);
</script>
{% endblock %}
