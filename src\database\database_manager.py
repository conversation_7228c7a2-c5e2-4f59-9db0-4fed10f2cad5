"""
نظام إدارة قاعدة البيانات
Database Management System
"""

import sqlite3
import json
import pickle
from datetime import datetime, timedelta
from pathlib import Path
from typing import List, Dict, Any, Optional, Tuple
import threading
from contextlib import contextmanager

from ..utils.logger import get_logger


class DatabaseManager:
    """مدير قاعدة البيانات الرئيسي"""
    
    def __init__(self, db_path: str = "data/face_recognition.db"):
        self.db_path = Path(db_path)
        self.db_path.parent.mkdir(parents=True, exist_ok=True)
        self.logger = get_logger()
        self._lock = threading.Lock()
        
        # إنشاء الجداول
        self._create_tables()
        self.logger.log_database_event("INIT", f"تم تهيئة قاعدة البيانات: {db_path}")
    
    @contextmanager
    def get_connection(self):
        """الحصول على اتصال آمن بقاعدة البيانات"""
        conn = None
        try:
            conn = sqlite3.connect(str(self.db_path), timeout=30.0)
            conn.row_factory = sqlite3.Row  # للوصول للأعمدة بالاسم
            yield conn
        except Exception as e:
            if conn:
                conn.rollback()
            self.logger.error(f"خطأ في قاعدة البيانات: {e}")
            raise
        finally:
            if conn:
                conn.close()
    
    def _create_tables(self):
        """إنشاء جداول قاعدة البيانات"""
        with self.get_connection() as conn:
            cursor = conn.cursor()
            
            # جدول الأشخاص
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS persons (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    name TEXT UNIQUE NOT NULL,
                    full_name TEXT,
                    email TEXT,
                    phone TEXT,
                    department TEXT,
                    position TEXT,
                    notes TEXT,
                    image_path TEXT,
                    encoding_data BLOB,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    is_active BOOLEAN DEFAULT 1
                )
            """)

            # جدول المستخدمين
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS users (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    national_id TEXT NOT NULL UNIQUE,
                    username TEXT NOT NULL UNIQUE,
                    password_hash TEXT NOT NULL,
                    full_name TEXT,
                    email TEXT,
                    role TEXT DEFAULT 'user',
                    is_active BOOLEAN DEFAULT 1,
                    last_login TIMESTAMP,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            """)

            # جدول سجل التعرف
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS recognition_log (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    person_id INTEGER,
                    person_name TEXT,
                    confidence REAL,
                    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    camera_id INTEGER DEFAULT 0,
                    image_path TEXT,
                    location_data TEXT,
                    FOREIGN KEY (person_id) REFERENCES persons (id)
                )
            """)
            
            # جدول الوجوه غير المعروفة
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS unknown_faces (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    camera_id INTEGER DEFAULT 0,
                    image_path TEXT,
                    encoding_data BLOB,
                    location_data TEXT,
                    is_processed BOOLEAN DEFAULT 0
                )
            """)
            
            # جدول إحصائيات النظام
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS system_statistics (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    date DATE UNIQUE,
                    total_recognitions INTEGER DEFAULT 0,
                    unique_persons INTEGER DEFAULT 0,
                    unknown_faces INTEGER DEFAULT 0,
                    average_confidence REAL DEFAULT 0,
                    system_uptime INTEGER DEFAULT 0,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            """)
            
            # جدول أحداث النظام
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS system_events (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    event_type TEXT NOT NULL,
                    event_data TEXT,
                    severity TEXT DEFAULT 'INFO',
                    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    source TEXT
                )
            """)
            
            # جدول إعدادات النظام
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS system_settings (
                    key TEXT PRIMARY KEY,
                    value TEXT,
                    description TEXT,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            """)
            
            # إنشاء فهارس لتحسين الأداء
            cursor.execute("CREATE INDEX IF NOT EXISTS idx_recognition_timestamp ON recognition_log(timestamp)")
            cursor.execute("CREATE INDEX IF NOT EXISTS idx_recognition_person ON recognition_log(person_id)")
            cursor.execute("CREATE INDEX IF NOT EXISTS idx_unknown_timestamp ON unknown_faces(timestamp)")
            cursor.execute("CREATE INDEX IF NOT EXISTS idx_events_timestamp ON system_events(timestamp)")
            cursor.execute("CREATE INDEX IF NOT EXISTS idx_events_type ON system_events(event_type)")
            
            conn.commit()
    
    # === إدارة الأشخاص ===
    
    def add_person(self, name: str, full_name: str = None, email: str = None,
                   phone: str = None, department: str = None, position: str = None,
                   notes: str = None, image_path: str = None, 
                   encoding_data: bytes = None) -> int:
        """إضافة شخص جديد"""
        with self._lock:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                
                cursor.execute("""
                    INSERT INTO persons (name, full_name, email, phone, department, 
                                       position, notes, image_path, encoding_data)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
                """, (name, full_name, email, phone, department, position, 
                      notes, image_path, encoding_data))
                
                person_id = cursor.lastrowid
                conn.commit()
                
                self.logger.log_database_event("ADD_PERSON", f"تم إضافة شخص: {name} (ID: {person_id})")
                return person_id
    
    def get_person(self, person_id: int) -> Optional[Dict]:
        """الحصول على بيانات شخص"""
        with self.get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute("SELECT * FROM persons WHERE id = ? AND is_active = 1", (person_id,))
            row = cursor.fetchone()
            if row:
                person = dict(row)
                # إزالة encoding_data من النتيجة
                if 'encoding_data' in person:
                    del person['encoding_data']
                return person
            return None
    
    def get_person_by_name(self, name: str) -> Optional[Dict]:
        """الحصول على بيانات شخص بالاسم"""
        with self.get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute("SELECT * FROM persons WHERE name = ? AND is_active = 1", (name,))
            row = cursor.fetchone()
            if row:
                person = dict(row)
                # إزالة encoding_data من النتيجة
                if 'encoding_data' in person:
                    del person['encoding_data']
                return person
            return None
    
    def get_all_persons(self) -> List[Dict]:
        """الحصول على جميع الأشخاص"""
        with self.get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute("SELECT * FROM persons WHERE is_active = 1 ORDER BY name")
            persons = []
            for row in cursor.fetchall():
                person = dict(row)
                # إزالة encoding_data من النتيجة لأنها لا تحتاج للعرض
                if 'encoding_data' in person:
                    del person['encoding_data']
                persons.append(person)
            return persons
    
    def update_person(self, person_id: int, **kwargs) -> bool:
        """تحديث بيانات شخص"""
        if not kwargs:
            return False
        
        # إضافة وقت التحديث
        kwargs['updated_at'] = datetime.now()
        
        with self._lock:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                
                # بناء استعلام التحديث
                fields = ", ".join([f"{key} = ?" for key in kwargs.keys()])
                values = list(kwargs.values()) + [person_id]
                
                cursor.execute(f"UPDATE persons SET {fields} WHERE id = ?", values)
                success = cursor.rowcount > 0
                conn.commit()
                
                if success:
                    self.logger.log_database_event("UPDATE_PERSON", f"تم تحديث شخص ID: {person_id}")
                
                return success
    
    def delete_person(self, person_id: int) -> bool:
        """حذف شخص (حذف منطقي)"""
        with self._lock:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute("UPDATE persons SET is_active = 0 WHERE id = ?", (person_id,))
                success = cursor.rowcount > 0
                conn.commit()
                
                if success:
                    self.logger.log_database_event("DELETE_PERSON", f"تم حذف شخص ID: {person_id}")
                
                return success

    # === سجل التعرف ===

    def log_recognition(self, person_id: int, person_name: str, confidence: float,
                       camera_id: int = 0, image_path: str = None,
                       location_data: str = None) -> int:
        """تسجيل حدث تعرف"""
        with self._lock:
            with self.get_connection() as conn:
                cursor = conn.cursor()

                cursor.execute("""
                    INSERT INTO recognition_log (person_id, person_name, confidence,
                                               camera_id, image_path, location_data)
                    VALUES (?, ?, ?, ?, ?, ?)
                """, (person_id, person_name, confidence, camera_id, image_path, location_data))

                log_id = cursor.lastrowid
                conn.commit()

                # تحديث الإحصائيات اليومية
                self._update_daily_statistics()

                return log_id

    def log_unknown_face(self, camera_id: int = 0, image_path: str = None,
                        encoding_data: bytes = None, location_data: str = None) -> int:
        """تسجيل وجه غير معروف"""
        with self._lock:
            with self.get_connection() as conn:
                cursor = conn.cursor()

                cursor.execute("""
                    INSERT INTO unknown_faces (camera_id, image_path, encoding_data, location_data)
                    VALUES (?, ?, ?, ?)
                """, (camera_id, image_path, encoding_data, location_data))

                log_id = cursor.lastrowid
                conn.commit()

                return log_id

    def get_recognition_history(self, person_id: int = None, days: int = 30) -> List[Dict]:
        """الحصول على تاريخ التعرف"""
        with self.get_connection() as conn:
            cursor = conn.cursor()

            since_date = datetime.now() - timedelta(days=days)

            if person_id:
                cursor.execute("""
                    SELECT * FROM recognition_log
                    WHERE person_id = ? AND timestamp >= ?
                    ORDER BY timestamp DESC
                """, (person_id, since_date))
            else:
                cursor.execute("""
                    SELECT * FROM recognition_log
                    WHERE timestamp >= ?
                    ORDER BY timestamp DESC
                """, (since_date,))

            return [dict(row) for row in cursor.fetchall()]

    def cleanup_old_data(self, days: int = 90):
        """تنظيف البيانات القديمة"""
        cutoff_date = datetime.now() - timedelta(days=days)

        with self._lock:
            with self.get_connection() as conn:
                cursor = conn.cursor()

                # حذف سجلات التعرف القديمة
                cursor.execute("DELETE FROM recognition_log WHERE timestamp < ?", (cutoff_date,))
                recognition_deleted = cursor.rowcount

                # حذف الوجوه غير المعروفة القديمة
                cursor.execute("DELETE FROM unknown_faces WHERE timestamp < ?", (cutoff_date,))
                unknown_deleted = cursor.rowcount

                conn.commit()

                self.logger.log_database_event("CLEANUP",
                    f"تم حذف {recognition_deleted} سجل تعرف، {unknown_deleted} وجه غير معروف")

    def _update_daily_statistics(self):
        """تحديث الإحصائيات اليومية"""
        today = datetime.now().strftime('%Y-%m-%d')

        with self.get_connection() as conn:
            cursor = conn.cursor()

            # حساب الإحصائيات لليوم الحالي
            cursor.execute("""
                SELECT
                    COUNT(*) as total_recognitions,
                    COUNT(DISTINCT person_id) as unique_persons,
                    AVG(confidence) as avg_confidence
                FROM recognition_log
                WHERE DATE(timestamp) = ?
            """, (today,))

            stats = cursor.fetchone()

            # حساب الوجوه غير المعروفة
            cursor.execute("""
                SELECT COUNT(*) FROM unknown_faces
                WHERE DATE(timestamp) = ?
            """, (today,))

            unknown_count = cursor.fetchone()[0]

            # تحديث أو إدراج الإحصائيات
            cursor.execute("""
                INSERT OR REPLACE INTO system_statistics
                (date, total_recognitions, unique_persons, unknown_faces, average_confidence)
                VALUES (?, ?, ?, ?, ?)
            """, (today, stats[0], stats[1], unknown_count, stats[2] or 0))

            conn.commit()

    def log_system_event(self, event_type: str, event_data: str = None,
                        severity: str = "INFO", source: str = None) -> int:
        """تسجيل حدث نظام"""
        with self._lock:
            with self.get_connection() as conn:
                cursor = conn.cursor()

                cursor.execute("""
                    INSERT INTO system_events (event_type, event_data, severity, source)
                    VALUES (?, ?, ?, ?)
                """, (event_type, event_data, severity, source))

                event_id = cursor.lastrowid
                conn.commit()

                return event_id

    def get_system_events(self, event_type: str = None, hours: int = 24) -> List[Dict]:
        """الحصول على أحداث النظام"""
        with self.get_connection() as conn:
            cursor = conn.cursor()

            since_time = datetime.now() - timedelta(hours=hours)

            if event_type:
                cursor.execute("""
                    SELECT * FROM system_events
                    WHERE event_type = ? AND timestamp >= ?
                    ORDER BY timestamp DESC
                """, (event_type, since_time))
            else:
                cursor.execute("""
                    SELECT * FROM system_events
                    WHERE timestamp >= ?
                    ORDER BY timestamp DESC
                """, (since_time,))

            return [dict(row) for row in cursor.fetchall()]

    def get_database_info(self) -> Dict:
        """الحصول على معلومات قاعدة البيانات"""
        with self.get_connection() as conn:
            cursor = conn.cursor()

            info = {}

            # حجم قاعدة البيانات
            info['file_size'] = self.db_path.stat().st_size if self.db_path.exists() else 0

            # عدد الجداول
            cursor.execute("SELECT COUNT(*) FROM sqlite_master WHERE type='table'")
            info['table_count'] = cursor.fetchone()[0]

            # إحصائيات الجداول
            tables = ['persons', 'recognition_log', 'unknown_faces', 'system_events', 'users']
            for table in tables:
                try:
                    cursor.execute(f"SELECT COUNT(*) FROM {table}")
                    info[f'{table}_count'] = cursor.fetchone()[0]
                except:
                    info[f'{table}_count'] = 0

            return info

    # ==================== دوال إدارة المستخدمين ====================

    def create_user(self, national_id: str, username: str, password_hash: str,
                   full_name: str = None, email: str = None, role: str = 'user') -> int:
        """إنشاء مستخدم جديد"""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()

                cursor.execute("""
                    INSERT INTO users (national_id, username, password_hash, full_name, email, role)
                    VALUES (?, ?, ?, ?, ?, ?)
                """, (national_id, username, password_hash, full_name, email, role))

                user_id = cursor.lastrowid
                conn.commit()  # تأكيد الحفظ
                self.logger.info(f"تم إنشاء مستخدم جديد: {username} (ID: {user_id})")
                return user_id
        except Exception as e:
            self.logger.error(f"خطأ في إنشاء المستخدم {username}: {e}")
            raise

    def get_user_by_username(self, username: str) -> Dict:
        """الحصول على مستخدم بالاسم"""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()

                cursor.execute("""
                    SELECT * FROM users WHERE username = ? AND is_active = 1
                """, (username,))

                row = cursor.fetchone()
                return dict(row) if row else None
        except Exception as e:
            self.logger.error(f"خطأ في البحث عن المستخدم بالاسم {username}: {e}")
            return None

    def get_user_by_national_id(self, national_id: str) -> Dict:
        """الحصول على مستخدم برقم الهوية"""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()

                cursor.execute("""
                    SELECT * FROM users WHERE national_id = ? AND is_active = 1
                """, (national_id,))

                row = cursor.fetchone()
                return dict(row) if row else None
        except Exception as e:
            self.logger.error(f"خطأ في البحث عن المستخدم برقم الهوية {national_id}: {e}")
            return None

    def update_last_login(self, user_id: int):
        """تحديث آخر تسجيل دخول"""
        with self.get_connection() as conn:
            cursor = conn.cursor()

            cursor.execute("""
                UPDATE users SET last_login = CURRENT_TIMESTAMP
                WHERE id = ?
            """, (user_id,))

            self.logger.info(f"تم تحديث آخر تسجيل دخول للمستخدم ID: {user_id}")

    def get_all_users(self) -> List[Dict]:
        """الحصول على جميع المستخدمين"""
        with self.get_connection() as conn:
            cursor = conn.cursor()

            cursor.execute("""
                SELECT id, national_id, username, full_name, email, role,
                       is_active, last_login, created_at
                FROM users
                ORDER BY created_at DESC
            """)

            return [dict(row) for row in cursor.fetchall()]

    def get_recent_recognitions(self, limit: int = 10) -> List[Dict]:
        """الحصول على آخر عمليات التعرف"""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()

                cursor.execute("""
                    SELECT * FROM recognition_log
                    ORDER BY timestamp DESC
                    LIMIT ?
                """, (limit,))

                return [dict(row) for row in cursor.fetchall()]
        except Exception as e:
            self.logger.error(f"خطأ في الحصول على التعرف الأخير: {e}")
            return []

    def add_unknown_face(self, face_encoding: bytes, image_path: str = None, confidence: float = 0.0) -> int:
        """إضافة وجه غير معروف"""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()

                cursor.execute("""
                    INSERT INTO unknown_faces (face_encoding, image_path, confidence)
                    VALUES (?, ?, ?)
                """, (face_encoding, image_path, confidence))

                face_id = cursor.lastrowid
                conn.commit()
                self.logger.info(f"تم حفظ وجه غير معروف: ID {face_id}")
                return face_id
        except Exception as e:
            self.logger.error(f"خطأ في حفظ الوجه غير المعروف: {e}")
            return 0

    def add_recognition_log(self, person_name: str, confidence: float, camera_id: int = 0) -> int:
        """إضافة سجل تعرف"""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()

                cursor.execute("""
                    INSERT INTO recognition_log (person_name, confidence, camera_id)
                    VALUES (?, ?, ?)
                """, (person_name, confidence, camera_id))

                log_id = cursor.lastrowid
                conn.commit()
                self.logger.info(f"تم تسجيل التعرف: {person_name} بثقة {confidence:.2f}")
                return log_id
        except Exception as e:
            self.logger.error(f"خطأ في تسجيل التعرف: {e}")
            return 0
