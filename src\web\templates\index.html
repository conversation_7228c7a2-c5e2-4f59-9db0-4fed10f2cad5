{% extends "base.html" %}

{% block title %}نظام التعرف على الوجوه الاحترافي{% endblock %}

{% block content %}
<div class="text-center mb-5">
    <h1 class="display-4 mb-3">
        <i class="bi bi-camera-video text-primary me-3"></i>
        نظام التعرف على الوجوه الاحترافي
    </h1>
    <p class="lead text-muted">نظام متقدم للتعرف على الوجوه مع ميزات الأمان والمراقبة</p>
</div>

<div class="row mb-5">
    <div class="col-md-4">
        <div class="card h-100 text-center">
            <div class="card-body">
                <i class="bi bi-speedometer2 text-primary" style="font-size: 3rem;"></i>
                <h5 class="card-title mt-3">لوحة التحكم</h5>
                <p class="card-text">مراقبة النظام في الوقت الفعلي وعرض الإحصائيات المباشرة</p>
                <a href="/dashboard" class="btn btn-primary">الانتقال للوحة التحكم</a>
            </div>
        </div>
    </div>
    <div class="col-md-4">
        <div class="card h-100 text-center">
            <div class="card-body">
                <i class="bi bi-people text-success" style="font-size: 3rem;"></i>
                <h5 class="card-title mt-3">إدارة الأشخاص</h5>
                <p class="card-text">إضافة وتعديل وحذف الأشخاص المسجلين في النظام</p>
                <a href="/persons" class="btn btn-success">إدارة الأشخاص</a>
            </div>
        </div>
    </div>
    <div class="col-md-4">
        <div class="card h-100 text-center">
            <div class="card-body">
                <i class="bi bi-gear text-warning" style="font-size: 3rem;"></i>
                <h5 class="card-title mt-3">الإعدادات</h5>
                <p class="card-text">تخصيص إعدادات النظام والكاميرا والأمان</p>
                <a href="/settings" class="btn btn-warning">الإعدادات</a>
            </div>
        </div>
    </div>
</div>

<div class="row mb-5">
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="bi bi-info-circle me-2"></i>حول النظام</h5>
            </div>
            <div class="card-body">
                <p>نظام التعرف على الوجوه الاحترافي هو حل متكامل يوفر:</p>
                <ul class="list-unstyled">
                    <li><i class="bi bi-check-circle text-success me-2"></i>التعرف على الوجوه في الوقت الفعلي</li>
                    <li><i class="bi bi-check-circle text-success me-2"></i>نظام أمان متقدم مع كشف التلاعب</li>
                    <li><i class="bi bi-check-circle text-success me-2"></i>تسجيل الفيديو والتنبيهات</li>
                    <li><i class="bi bi-check-circle text-success me-2"></i>واجهة ويب سهلة الاستخدام</li>
                    <li><i class="bi bi-check-circle text-success me-2"></i>قاعدة بيانات شاملة للسجلات</li>
                    <li><i class="bi bi-check-circle text-success me-2"></i>مراقبة الأداء والإحصائيات</li>
                </ul>
            </div>
        </div>
    </div>
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="bi bi-graph-up me-2"></i>إحصائيات سريعة</h5>
            </div>
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-6">
                        <div class="metric-card mb-3">
                            <div class="metric-value" id="total-persons">0</div>
                            <div class="metric-label">الأشخاص المسجلون</div>
                        </div>
                    </div>
                    <div class="col-6">
                        <div class="metric-card mb-3">
                            <div class="metric-value" id="total-recognitions">0</div>
                            <div class="metric-label">إجمالي التعرفات</div>
                        </div>
                    </div>
                    <div class="col-6">
                        <div class="metric-card">
                            <div class="metric-value" id="today-recognitions">0</div>
                            <div class="metric-label">تعرفات اليوم</div>
                        </div>
                    </div>
                    <div class="col-6">
                        <div class="metric-card">
                            <div class="metric-value" id="system-uptime">00:00:00</div>
                            <div class="metric-label">وقت التشغيل</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="card">
    <div class="card-header">
        <h5 class="mb-0"><i class="bi bi-clock-history me-2"></i>آخر الأنشطة</h5>
    </div>
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-hover">
                <thead class="table-light">
                    <tr>
                        <th>الوقت</th>
                        <th>النشاط</th>
                        <th>التفاصيل</th>
                        <th>الحالة</th>
                    </tr>
                </thead>
                <tbody id="recent-activities">
                    <tr>
                        <td colspan="4" class="text-center text-muted">جاري تحميل الأنشطة...</td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        loadQuickStats();
        loadRecentActivities();
    });
    
    async function loadQuickStats() {
        try {
            const [stats, persons] = await Promise.all([
                apiCall('/api/statistics'),
                apiCall('/api/persons')
            ]);
            
            // Update quick stats
            document.getElementById('total-persons').textContent = formatNumber(persons.length);
            
            if (stats.performance) {
                document.getElementById('total-recognitions').textContent = 
                    formatNumber(stats.performance.total_frames_processed || 0);
                document.getElementById('system-uptime').textContent = 
                    stats.performance.session_duration || '00:00:00';
            }
            
            // Load today's recognitions
            const todayLogs = await apiCall('/api/recognition-log?days=1');
            document.getElementById('today-recognitions').textContent = formatNumber(todayLogs.length);
            
        } catch (error) {
            console.error('Failed to load quick stats:', error);
        }
    }
    
    async function loadRecentActivities() {
        try {
            const activities = await apiCall('/api/recognition-log?days=1');
            const tbody = document.getElementById('recent-activities');
            
            if (!activities || activities.length === 0) {
                tbody.innerHTML = '<tr><td colspan="4" class="text-center text-muted">لا توجد أنشطة حديثة</td></tr>';
                return;
            }
            
            tbody.innerHTML = activities.slice(0, 5).map(activity => `
                <tr>
                    <td>${formatDateTime(activity.timestamp)}</td>
                    <td>تعرف على وجه</td>
                    <td>${activity.person_name} - دقة ${Math.round(activity.confidence)}%</td>
                    <td>
                        <span class="badge bg-success">
                            <i class="bi bi-check-circle"></i> نجح
                        </span>
                    </td>
                </tr>
            `).join('');
            
        } catch (error) {
            console.error('Failed to load recent activities:', error);
            document.getElementById('recent-activities').innerHTML = 
                '<tr><td colspan="4" class="text-center text-danger">فشل في تحميل الأنشطة</td></tr>';
        }
    }
    
    // Auto-refresh every 30 seconds
    setInterval(() => {
        loadQuickStats();
        loadRecentActivities();
    }, 30000);
</script>
{% endblock %}
