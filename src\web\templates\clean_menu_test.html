<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار القائمة النظيفة</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: white;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        
        .sidebar {
            background: linear-gradient(180deg, #2c3e50 0%, #34495e 100%);
            min-height: 100vh;
            box-shadow: 2px 0 10px rgba(0, 0, 0, 0.1);
        }
        
        .nav-link {
            color: rgba(255, 255, 255, 0.8);
            padding: 12px 20px;
            margin: 5px 0;
            border-radius: 8px;
            transition: all 0.3s ease;
            text-decoration: none;
            display: flex;
            align-items: center;
        }
        
        .nav-link:hover {
            background: rgba(255, 255, 255, 0.1);
            color: white;
            transform: translateX(5px);
        }
        
        .nav-link.active {
            background: linear-gradient(135deg, #3498db, #2980b9);
            color: white;
        }
        
        .test-info {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 10px;
            padding: 20px;
            margin: 20px;
            backdrop-filter: blur(10px);
        }
        
        .admin-link {
            background: linear-gradient(135deg, rgba(220, 53, 69, 0.2), rgba(220, 53, 69, 0.1));
            border-left: 4px solid #dc3545;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- القائمة الجانبية النظيفة -->
            <div class="col-md-3 sidebar p-0">
                <div class="p-3">
                    <!-- عنوان القائمة -->
                    <div class="text-center mb-4">
                        <h5 class="text-white">القائمة الرئيسية</h5>
                        <small class="text-muted">نظام التعرف على الوجوه</small>
                        <hr class="text-white-50">
                    </div>
                    
                    <!-- القائمة النظيفة بدون تكرار -->
                    <nav class="nav flex-column">
                        <a class="nav-link" href="/dashboard">
                            <i class="bi bi-speedometer2 me-2"></i>
                            لوحة التحكم العامة
                        </a>
                        <a class="nav-link admin-link" href="/admin">
                            <i class="bi bi-shield-check me-2"></i>
                            لوحة التحكم الإدارية
                        </a>
                        <a class="nav-link" href="/persons">
                            <i class="bi bi-people me-2"></i>
                            إدارة الأشخاص
                        </a>
                        <a class="nav-link" href="/recognition">
                            <i class="bi bi-camera-video me-2"></i>
                            التعرف على الوجوه
                        </a>
                        <a class="nav-link" href="/camera-test">
                            <i class="bi bi-camera me-2"></i>
                            تشخيص الكاميرا
                        </a>
                        <a class="nav-link" href="/logs">
                            <i class="bi bi-journal-text me-2"></i>
                            السجلات
                        </a>
                        <a class="nav-link" href="/settings">
                            <i class="bi bi-gear me-2"></i>
                            الإعدادات
                        </a>
                    </nav>
                </div>
            </div>
            
            <!-- المحتوى الرئيسي -->
            <div class="col-md-9">
                <div class="p-4">
                    <h1 class="text-center mb-4">🧹 اختبار القائمة النظيفة</h1>
                    
                    <div class="test-info">
                        <h3>✅ تم حذف جميع الأزرار المكررة:</h3>
                        <ul>
                            <li>✅ زر واحد فقط لـ "لوحة التحكم الإدارية"</li>
                            <li>✅ لا توجد أزرار مكررة</li>
                            <li>✅ قائمة نظيفة ومرتبة</li>
                            <li>✅ تصميم متسق</li>
                        </ul>
                    </div>
                    
                    <div class="test-info">
                        <h3>📋 القائمة النهائية:</h3>
                        <ol>
                            <li>📊 لوحة التحكم العامة</li>
                            <li>🛡️ لوحة التحكم الإدارية (زر واحد فقط)</li>
                            <li>👥 إدارة الأشخاص</li>
                            <li>🎥 التعرف على الوجوه</li>
                            <li>📷 تشخيص الكاميرا</li>
                            <li>📋 السجلات</li>
                            <li>⚙️ الإعدادات</li>
                        </ol>
                    </div>
                    
                    <div class="test-info">
                        <h3>🎯 التعليمات:</h3>
                        <ol>
                            <li>تحقق من القائمة الجانبية</li>
                            <li>يجب أن ترى زر واحد فقط لـ "لوحة التحكم الإدارية"</li>
                            <li>الزر له تصميم أحمر مميز</li>
                            <li>اضغط عليه للانتقال إلى /admin</li>
                        </ol>
                    </div>
                    
                    <div class="text-center mt-4">
                        <a href="/admin" class="btn btn-danger btn-lg">
                            <i class="bi bi-shield-check me-2"></i>
                            الذهاب إلى لوحة التحكم الإدارية
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <script>
        console.log('🧪 صفحة اختبار القائمة النظيفة');
        
        // عد الأزرار المكررة
        document.addEventListener('DOMContentLoaded', function() {
            const adminLinks = document.querySelectorAll('a[href="/admin"]');
            console.log(`عدد أزرار لوحة التحكم الإدارية: ${adminLinks.length}`);
            
            if (adminLinks.length === 1) {
                console.log('✅ ممتاز! زر واحد فقط');
            } else {
                console.log('❌ يوجد أزرار مكررة');
            }
            
            // إضافة تأثير بصري للزر الإداري
            adminLinks.forEach(link => {
                link.style.boxShadow = '0 0 15px rgba(220, 53, 69, 0.5)';
            });
        });
    </script>
</body>
</html>
