#!/usr/bin/env python3
"""
اختبار سريع لنظام التعرف على الوجوه
Quick Test for Face Recognition System
"""

import requests
import time
import json

def test_web_apis():
    """اختبار APIs واجهة الويب"""
    base_url = "http://localhost:5000"
    
    print("🧪 اختبار APIs واجهة الويب...")
    print("=" * 50)
    
    # اختبار حالة النظام
    try:
        response = requests.get(f"{base_url}/api/status")
        if response.status_code == 200:
            print("✅ API حالة النظام يعمل")
            data = response.json()
            print(f"   📊 الإحصائيات: {data.get('statistics', {})}")
        else:
            print("❌ API حالة النظام لا يعمل")
    except Exception as e:
        print(f"❌ خطأ في API حالة النظام: {e}")
    
    # اختبار قائمة الأشخاص
    try:
        response = requests.get(f"{base_url}/api/persons")
        if response.status_code == 200:
            print("✅ API قائمة الأشخاص يعمل")
            persons = response.json()
            print(f"   👥 عدد الأشخاص المسجلين: {len(persons)}")
            for person in persons[:3]:  # أول 3 أشخاص
                print(f"      - {person.get('name', 'غير معروف')}")
        else:
            print("❌ API قائمة الأشخاص لا يعمل")
    except Exception as e:
        print(f"❌ خطأ في API قائمة الأشخاص: {e}")
    
    # اختبار سجل التعرف
    try:
        response = requests.get(f"{base_url}/api/recognition-log?days=1")
        if response.status_code == 200:
            print("✅ API سجل التعرف يعمل")
            logs = response.json()
            print(f"   📝 عدد سجلات اليوم: {len(logs)}")
        else:
            print("❌ API سجل التعرف لا يعمل")
    except Exception as e:
        print(f"❌ خطأ في API سجل التعرف: {e}")
    
    # اختبار الإعدادات
    try:
        response = requests.get(f"{base_url}/api/settings")
        if response.status_code == 200:
            print("✅ API الإعدادات يعمل")
            settings = response.json()
            print(f"   ⚙️ إعدادات الكاميرا: {settings.get('camera', {}).get('resolution_width', 'غير محدد')}x{settings.get('camera', {}).get('resolution_height', 'غير محدد')}")
        else:
            print("❌ API الإعدادات لا يعمل")
    except Exception as e:
        print(f"❌ خطأ في API الإعدادات: {e}")
    
    # اختبار تقرير الأمان
    try:
        response = requests.get(f"{base_url}/api/security/report")
        if response.status_code == 200:
            print("✅ API تقرير الأمان يعمل")
            report = response.json()
            print(f"   🛡️ حالة المراقبة: {'نشط' if report.get('session_stats', {}).get('monitoring_active') else 'معطل'}")
        else:
            print("❌ API تقرير الأمان لا يعمل")
    except Exception as e:
        print(f"❌ خطأ في API تقرير الأمان: {e}")
    
    # اختبار التحكم في النظام
    print("\n🎮 اختبار التحكم في النظام...")
    
    # إيقاف النظام
    try:
        response = requests.post(f"{base_url}/api/system/stop")
        if response.status_code == 200:
            print("✅ API إيقاف النظام يعمل")
            result = response.json()
            print(f"   📄 الرسالة: {result.get('message', 'لا توجد رسالة')}")
        else:
            print("❌ API إيقاف النظام لا يعمل")
    except Exception as e:
        print(f"❌ خطأ في API إيقاف النظام: {e}")
    
    # انتظار قصير
    time.sleep(2)
    
    # بدء النظام
    try:
        response = requests.post(f"{base_url}/api/system/start")
        if response.status_code == 200:
            print("✅ API بدء النظام يعمل")
            result = response.json()
            print(f"   📄 الرسالة: {result.get('message', 'لا توجد رسالة')}")
        else:
            print("❌ API بدء النظام لا يعمل")
    except Exception as e:
        print(f"❌ خطأ في API بدء النظام: {e}")

def test_web_pages():
    """اختبار صفحات الويب"""
    base_url = "http://localhost:5000"
    pages = [
        ("الصفحة الرئيسية", "/"),
        ("لوحة التحكم", "/dashboard"),
        ("إدارة الأشخاص", "/persons"),
        ("السجلات", "/logs"),
        ("الإعدادات", "/settings")
    ]
    
    print("\n🌐 اختبار صفحات الويب...")
    print("=" * 50)
    
    for page_name, page_url in pages:
        try:
            response = requests.get(f"{base_url}{page_url}")
            if response.status_code == 200:
                print(f"✅ {page_name} تعمل")
            else:
                print(f"❌ {page_name} لا تعمل (كود: {response.status_code})")
        except Exception as e:
            print(f"❌ خطأ في {page_name}: {e}")

def test_file_structure():
    """اختبار هيكل الملفات"""
    import os
    from pathlib import Path
    
    print("\n📁 اختبار هيكل الملفات...")
    print("=" * 50)
    
    required_files = [
        "main.py",
        "web_app.py",
        "quick_start.py",
        "requirements.txt",
        "config/settings.json",
        "src/core/face_recognition_system.py",
        "src/web/web_server.py",
        "src/database/database_manager.py"
    ]
    
    required_dirs = [
        "src",
        "data",
        "config",
        "data/persons",
        "data/logs"
    ]
    
    for file_path in required_files:
        if os.path.exists(file_path):
            print(f"✅ {file_path}")
        else:
            print(f"❌ {file_path} مفقود")
    
    for dir_path in required_dirs:
        if os.path.exists(dir_path):
            print(f"✅ {dir_path}/")
        else:
            print(f"❌ {dir_path}/ مفقود")
    
    # فحص صور الأشخاص
    persons_dir = Path("data/persons")
    if persons_dir.exists():
        images = list(persons_dir.glob("*.jpg")) + list(persons_dir.glob("*.png"))
        print(f"📸 عدد صور الأشخاص: {len(images)}")
        for img in images[:5]:  # أول 5 صور
            print(f"   - {img.name}")
    else:
        print("❌ مجلد صور الأشخاص غير موجود")

def main():
    """الدالة الرئيسية"""
    print("🎯 اختبار شامل لنظام التعرف على الوجوه")
    print("=" * 60)
    
    # اختبار هيكل الملفات
    test_file_structure()
    
    # اختبار صفحات الويب
    test_web_pages()
    
    # اختبار APIs
    test_web_apis()
    
    print("\n" + "=" * 60)
    print("✅ انتهى الاختبار الشامل")
    print("💡 إذا كانت جميع الاختبارات ناجحة، فالنظام جاهز للاستخدام!")
    print("🌐 افتح المتصفح على: http://localhost:5000")

if __name__ == "__main__":
    main()
