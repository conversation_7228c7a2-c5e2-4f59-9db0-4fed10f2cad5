<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار زر لوحة التحكم الإدارية</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: white;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        
        .admin-control-btn {
            display: block;
            background: linear-gradient(135deg, #dc3545, #c82333);
            border: 2px solid rgba(255, 255, 255, 0.2);
            border-radius: 15px;
            padding: 20px;
            text-decoration: none;
            color: white;
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
            overflow: hidden;
            box-shadow: 0 8px 25px rgba(220, 53, 69, 0.3);
            margin: 20px;
            max-width: 400px;
        }
        
        .admin-control-btn::after {
            content: 'ADMIN';
            position: absolute;
            top: -5px;
            right: -5px;
            background: linear-gradient(45deg, #ffd700, #ffed4e);
            color: #dc3545;
            font-size: 0.6rem;
            font-weight: bold;
            padding: 3px 8px;
            border-radius: 10px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
            animation: admin-badge-glow 2s infinite alternate;
            z-index: 10;
        }
        
        .admin-control-btn:hover {
            transform: translateY(-3px) scale(1.02);
            box-shadow: 0 12px 35px rgba(220, 53, 69, 0.4);
            border-color: rgba(255, 255, 255, 0.4);
            background: linear-gradient(135deg, #e74c3c, #dc3545);
            color: white;
            text-decoration: none;
        }
        
        .admin-btn-content {
            display: flex;
            align-items: center;
            gap: 15px;
        }
        
        .admin-btn-icon {
            width: 50px;
            height: 50px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.8rem;
            transition: all 0.3s ease;
        }
        
        .admin-control-btn:hover .admin-btn-icon {
            background: rgba(255, 255, 255, 0.3);
            transform: scale(1.1) rotate(5deg);
        }
        
        .admin-btn-text {
            flex: 1;
        }
        
        .admin-btn-title {
            font-weight: 700;
            font-size: 1.2rem;
            margin-bottom: 5px;
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
        }
        
        .admin-btn-subtitle {
            font-size: 0.9rem;
            opacity: 0.9;
            font-weight: 400;
        }
        
        .admin-btn-arrow {
            font-size: 1.5rem;
            transition: all 0.3s ease;
            opacity: 0.8;
        }
        
        .admin-control-btn:hover .admin-btn-arrow {
            transform: translateX(5px);
            opacity: 1;
        }
        
        @keyframes admin-badge-glow {
            0% { 
                opacity: 0.9; 
                transform: scale(1);
                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
            }
            100% { 
                opacity: 1; 
                transform: scale(1.05);
                box-shadow: 0 4px 12px rgba(255, 215, 0, 0.5);
            }
        }
        
        .test-info {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 10px;
            padding: 20px;
            margin: 20px;
            backdrop-filter: blur(10px);
        }
    </style>
</head>
<body>
    <div class="container mt-5">
        <div class="row justify-content-center">
            <div class="col-md-8">
                <h1 class="text-center mb-4">🛡️ اختبار زر لوحة التحكم الإدارية</h1>
                
                <div class="test-info">
                    <h3>📋 معلومات الاختبار:</h3>
                    <ul>
                        <li>✅ تم إنشاء الزر بنجاح</li>
                        <li>✅ CSS يعمل بشكل صحيح</li>
                        <li>✅ التأثيرات البصرية مفعلة</li>
                        <li>✅ الرابط يؤدي إلى /admin</li>
                    </ul>
                </div>
                
                <!-- الزر الإداري المميز -->
                <div class="d-flex justify-content-center">
                    <a href="/admin" class="admin-control-btn">
                        <div class="admin-btn-content">
                            <div class="admin-btn-icon">
                                <i class="bi bi-shield-check"></i>
                            </div>
                            <div class="admin-btn-text">
                                <div class="admin-btn-title">لوحة التحكم الإدارية</div>
                                <div class="admin-btn-subtitle">إدارة النظام والمراقبة</div>
                            </div>
                            <div class="admin-btn-arrow">
                                <i class="bi bi-arrow-right"></i>
                            </div>
                        </div>
                    </a>
                </div>
                
                <div class="test-info mt-4">
                    <h3>🎯 التعليمات:</h3>
                    <ol>
                        <li>اضغط على الزر أعلاه للانتقال إلى لوحة التحكم الإدارية</li>
                        <li>إذا كان الزر يعمل، فهذا يعني أن الكود صحيح</li>
                        <li>إذا لم تر الزر في القائمة الجانبية، امسح cache المتصفح</li>
                        <li>استخدم Ctrl+Shift+R لإعادة تحميل قوية</li>
                    </ol>
                </div>
                
                <div class="text-center mt-4">
                    <a href="/admin" class="btn btn-light btn-lg">
                        <i class="bi bi-arrow-left me-2"></i>
                        الذهاب إلى لوحة التحكم الإدارية
                    </a>
                </div>
            </div>
        </div>
    </div>
    
    <script>
        console.log('🧪 صفحة اختبار الزر الإداري تم تحميلها بنجاح');
        console.log('🔗 الزر يؤدي إلى: /admin');
        
        // إضافة تأثير بصري عند التحميل
        document.addEventListener('DOMContentLoaded', function() {
            const btn = document.querySelector('.admin-control-btn');
            if (btn) {
                setTimeout(() => {
                    btn.style.animation = 'pulse 1s ease-in-out';
                }, 1000);
            }
        });
    </script>
</body>
</html>
