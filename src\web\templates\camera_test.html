{% extends "base.html" %}

{% block title %}اختبار الكاميرا - نظام التعرف على الوجوه{% endblock %}

{% block content %}
<div class="container mt-4">
    <h2><i class="bi bi-camera me-2"></i>اختبار وتشخيص الكاميرا</h2>
    <p class="text-muted">أداة شاملة لاختبار وتشخيص مشاكل الكاميرا</p>
    
    <!-- نتائج التشخيص -->
    <div class="row mb-4">
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h5><i class="bi bi-list-check me-2"></i>نتائج التشخيص</h5>
                </div>
                <div class="card-body">
                    <div id="diagnosis-results">
                        <div class="text-center text-muted">
                            <div class="spinner-border" role="status">
                                <span class="visually-hidden">جاري التشخيص...</span>
                            </div>
                            <p class="mt-2">جاري فحص النظام...</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h5><i class="bi bi-camera-video me-2"></i>معاينة الكاميرا</h5>
                </div>
                <div class="card-body text-center">
                    <video id="test-video" width="300" height="200" style="border: 2px solid #ddd; border-radius: 8px; background: #000;" autoplay muted></video>
                    <div class="mt-3">
                        <button id="start-test-btn" class="btn btn-primary me-2">
                            <i class="bi bi-play-fill me-1"></i>بدء الاختبار
                        </button>
                        <button id="stop-test-btn" class="btn btn-danger" disabled>
                            <i class="bi bi-stop-fill me-1"></i>إيقاف الاختبار
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- الكاميرات المتاحة -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5><i class="bi bi-camera me-2"></i>الكاميرات المتاحة</h5>
                </div>
                <div class="card-body">
                    <div id="available-cameras">
                        <p class="text-muted">جاري البحث عن الكاميرات...</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- معلومات المتصفح -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5><i class="bi bi-info-circle me-2"></i>معلومات المتصفح</h5>
                </div>
                <div class="card-body">
                    <div id="browser-info"></div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- حلول المشاكل -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5><i class="bi bi-tools me-2"></i>حلول المشاكل الشائعة</h5>
                </div>
                <div class="card-body">
                    <div class="accordion" id="troubleshootingAccordion">
                        <div class="accordion-item">
                            <h2 class="accordion-header">
                                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#permission-issue">
                                    مشكلة الأذونات
                                </button>
                            </h2>
                            <div id="permission-issue" class="accordion-collapse collapse" data-bs-parent="#troubleshootingAccordion">
                                <div class="accordion-body">
                                    <ol>
                                        <li>اضغط على أيقونة الكاميرا في شريط العنوان</li>
                                        <li>اختر "السماح" للوصول إلى الكاميرا</li>
                                        <li>أعد تحميل الصفحة</li>
                                        <li>إذا لم تظهر الأيقونة، اذهب إلى إعدادات المتصفح → الخصوصية والأمان → إعدادات الموقع → الكاميرا</li>
                                    </ol>
                                </div>
                            </div>
                        </div>
                        
                        <div class="accordion-item">
                            <h2 class="accordion-header">
                                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#camera-busy">
                                    الكاميرا مستخدمة
                                </button>
                            </h2>
                            <div id="camera-busy" class="accordion-collapse collapse" data-bs-parent="#troubleshootingAccordion">
                                <div class="accordion-body">
                                    <ol>
                                        <li>أغلق جميع التطبيقات التي قد تستخدم الكاميرا (Zoom, Skype, Teams, إلخ)</li>
                                        <li>أغلق جميع علامات التبويب الأخرى في المتصفح</li>
                                        <li>أعد تشغيل المتصفح</li>
                                        <li>إذا استمرت المشكلة، أعد تشغيل الجهاز</li>
                                    </ol>
                                </div>
                            </div>
                        </div>
                        
                        <div class="accordion-item">
                            <h2 class="accordion-header">
                                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#no-camera">
                                    لا توجد كاميرا
                                </button>
                            </h2>
                            <div id="no-camera" class="accordion-collapse collapse" data-bs-parent="#troubleshootingAccordion">
                                <div class="accordion-body">
                                    <ol>
                                        <li>تأكد من توصيل الكاميرا بالجهاز</li>
                                        <li>تحقق من تشغيل الكاميرا في إدارة الأجهزة</li>
                                        <li>حدث تعريفات الكاميرا</li>
                                        <li>جرب كاميرا أخرى إذا كانت متاحة</li>
                                    </ol>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="mt-4">
        <a href="/recognition" class="btn btn-primary">
            <i class="bi bi-arrow-left me-1"></i>العودة إلى التعرف على الوجوه
        </a>
    </div>
</div>

<script>
let testStream = null;

document.addEventListener('DOMContentLoaded', function() {
    runDiagnosis();
    setupEventListeners();
});

function setupEventListeners() {
    document.getElementById('start-test-btn').addEventListener('click', startCameraTest);
    document.getElementById('stop-test-btn').addEventListener('click', stopCameraTest);
}

async function runDiagnosis() {
    const resultsContainer = document.getElementById('diagnosis-results');
    const results = [];
    
    // فحص دعم المتصفح
    if (navigator.mediaDevices && navigator.mediaDevices.getUserMedia) {
        results.push({ status: 'success', message: 'المتصفح يدعم الوصول إلى الكاميرا' });
    } else {
        results.push({ status: 'error', message: 'المتصفح لا يدعم الوصول إلى الكاميرا' });
    }
    
    // فحص HTTPS
    if (location.protocol === 'https:' || location.hostname === 'localhost' || location.hostname === '127.0.0.1') {
        results.push({ status: 'success', message: 'البروتوكول آمن للوصول إلى الكاميرا' });
    } else {
        results.push({ status: 'warning', message: 'قد تحتاج إلى HTTPS للوصول إلى الكاميرا' });
    }
    
    // فحص الكاميرات المتاحة
    try {
        const devices = await navigator.mediaDevices.enumerateDevices();
        const videoDevices = devices.filter(device => device.kind === 'videoinput');
        
        if (videoDevices.length > 0) {
            results.push({ status: 'success', message: `تم العثور على ${videoDevices.length} كاميرا` });
            displayAvailableCameras(videoDevices);
        } else {
            results.push({ status: 'error', message: 'لم يتم العثور على أي كاميرا' });
        }
    } catch (error) {
        results.push({ status: 'error', message: 'خطأ في فحص الكاميرات: ' + error.message });
    }
    
    // فحص الأذونات
    try {
        const permissionStatus = await navigator.permissions.query({ name: 'camera' });
        if (permissionStatus.state === 'granted') {
            results.push({ status: 'success', message: 'تم منح إذن الكاميرا' });
        } else if (permissionStatus.state === 'denied') {
            results.push({ status: 'error', message: 'تم رفض إذن الكاميرا' });
        } else {
            results.push({ status: 'warning', message: 'إذن الكاميرا غير محدد' });
        }
    } catch (error) {
        results.push({ status: 'info', message: 'لا يمكن فحص أذونات الكاميرا' });
    }
    
    // عرض النتائج
    displayResults(results);
    
    // عرض معلومات المتصفح
    displayBrowserInfo();
}

function displayResults(results) {
    const container = document.getElementById('diagnosis-results');
    const html = results.map(result => {
        const iconClass = {
            'success': 'bi-check-circle text-success',
            'error': 'bi-x-circle text-danger',
            'warning': 'bi-exclamation-triangle text-warning',
            'info': 'bi-info-circle text-info'
        }[result.status];
        
        return `
            <div class="d-flex align-items-center mb-2">
                <i class="bi ${iconClass} me-2"></i>
                <span>${result.message}</span>
            </div>
        `;
    }).join('');
    
    container.innerHTML = html;
}

function displayAvailableCameras(cameras) {
    const container = document.getElementById('available-cameras');
    
    if (cameras.length === 0) {
        container.innerHTML = '<p class="text-muted">لم يتم العثور على كاميرات</p>';
        return;
    }
    
    const html = cameras.map((camera, index) => `
        <div class="d-flex justify-content-between align-items-center border-bottom py-2">
            <div>
                <strong>كاميرا ${index + 1}</strong>
                <br>
                <small class="text-muted">${camera.label || 'غير محدد'}</small>
            </div>
            <button class="btn btn-sm btn-outline-primary" onclick="testSpecificCamera('${camera.deviceId}')">
                اختبار
            </button>
        </div>
    `).join('');
    
    container.innerHTML = html;
}

function displayBrowserInfo() {
    const container = document.getElementById('browser-info');
    const info = {
        'المتصفح': navigator.userAgent,
        'البروتوكول': location.protocol,
        'المضيف': location.hostname,
        'المنفذ': location.port || 'افتراضي'
    };
    
    const html = Object.entries(info).map(([key, value]) => `
        <div class="row mb-2">
            <div class="col-md-3"><strong>${key}:</strong></div>
            <div class="col-md-9"><code>${value}</code></div>
        </div>
    `).join('');
    
    container.innerHTML = html;
}

async function startCameraTest() {
    try {
        const video = document.getElementById('test-video');
        const startBtn = document.getElementById('start-test-btn');
        const stopBtn = document.getElementById('stop-test-btn');
        
        startBtn.disabled = true;
        startBtn.innerHTML = '<span class="spinner-border spinner-border-sm me-1"></span>جاري الاتصال...';
        
        testStream = await navigator.mediaDevices.getUserMedia({ video: true });
        video.srcObject = testStream;
        
        startBtn.style.display = 'none';
        stopBtn.disabled = false;
        
        showAlert('تم تشغيل الكاميرا بنجاح!', 'success');
        
    } catch (error) {
        console.error('خطأ في اختبار الكاميرا:', error);
        showAlert('فشل في تشغيل الكاميرا: ' + error.message, 'danger');
        
        const startBtn = document.getElementById('start-test-btn');
        startBtn.disabled = false;
        startBtn.innerHTML = '<i class="bi bi-play-fill me-1"></i>بدء الاختبار';
    }
}

function stopCameraTest() {
    if (testStream) {
        testStream.getTracks().forEach(track => track.stop());
        testStream = null;
    }
    
    const video = document.getElementById('test-video');
    video.srcObject = null;
    
    const startBtn = document.getElementById('start-test-btn');
    const stopBtn = document.getElementById('stop-test-btn');
    
    startBtn.style.display = 'inline-block';
    startBtn.disabled = false;
    stopBtn.disabled = true;
    
    showAlert('تم إيقاف الكاميرا', 'info');
}

async function testSpecificCamera(deviceId) {
    try {
        const stream = await navigator.mediaDevices.getUserMedia({
            video: { deviceId: { exact: deviceId } }
        });
        
        const video = document.getElementById('test-video');
        video.srcObject = stream;
        
        // إيقاف الكاميرا السابقة
        if (testStream) {
            testStream.getTracks().forEach(track => track.stop());
        }
        testStream = stream;
        
        document.getElementById('start-test-btn').style.display = 'none';
        document.getElementById('stop-test-btn').disabled = false;
        
        showAlert('تم تشغيل الكاميرا المحددة بنجاح!', 'success');
        
    } catch (error) {
        showAlert('فشل في تشغيل الكاميرا المحددة: ' + error.message, 'danger');
    }
}

// تنظيف الموارد عند مغادرة الصفحة
window.addEventListener('beforeunload', function() {
    if (testStream) {
        testStream.getTracks().forEach(track => track.stop());
    }
});

console.log('🔧 تم تحميل صفحة تشخيص الكاميرا');
</script>
{% endblock %}
