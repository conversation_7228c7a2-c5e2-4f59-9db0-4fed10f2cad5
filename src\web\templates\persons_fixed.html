{% extends "base.html" %}

{% block title %}إدارة الأشخاص - نظام التعرف على الوجوه{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h2><i class="bi bi-people me-2"></i>إدارة الأشخاص</h2>
    <div>
        <button class="btn btn-primary" onclick="showAddModal()">
            <i class="bi bi-person-plus"></i> إضافة شخص جديد
        </button>
        <button class="btn btn-secondary ms-2" onclick="loadPersonsNow()" id="refresh-btn">
            <i class="bi bi-arrow-clockwise"></i> تحديث القائمة
        </button>
        <button class="btn btn-info ms-2" onclick="testAPI()">
            <i class="bi bi-bug"></i> اختبار API
        </button>
    </div>
</div>

<!-- Search and Filter -->
<div class="card mb-4">
    <div class="card-body">
        <div class="row">
            <div class="col-md-6">
                <div class="input-group">
                    <span class="input-group-text"><i class="bi bi-search"></i></span>
                    <input type="text" class="form-control" id="search-input" placeholder="البحث عن شخص..." onkeyup="filterPersons()">
                </div>
            </div>
            <div class="col-md-3">
                <select class="form-select" id="department-filter" onchange="filterPersons()">
                    <option value="">جميع الأقسام</option>
                </select>
            </div>
            <div class="col-md-3">
                <select class="form-select" id="status-filter" onchange="filterPersons()">
                    <option value="">جميع الحالات</option>
                    <option value="active">نشط</option>
                    <option value="inactive">غير نشط</option>
                </select>
            </div>
        </div>
    </div>
</div>

<!-- Persons Table -->
<div class="card">
    <div class="card-header">
        <h5 class="mb-0"><i class="bi bi-table me-2"></i>قائمة الأشخاص (<span id="persons-count">{{ persons|length }}</span>)</h5>
    </div>
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-hover table-custom">
                <thead class="table-dark">
                    <tr>
                        <th>الصورة</th>
                        <th>الاسم</th>
                        <th>الاسم الكامل</th>
                        <th>القسم</th>
                        <th>البريد الإلكتروني</th>
                        <th>الهاتف</th>
                        <th>تاريخ الإضافة</th>
                        <th>الحالة</th>
                        <th>الإجراءات</th>
                    </tr>
                </thead>
                <tbody id="persons-table">
                    {% if persons %}
                        {% for person in persons %}
                        <tr>
                            <td>
                                <div class="d-flex align-items-center justify-content-center">
                                    {% if person.image_path %}
                                        <img src="/static/persons/{{ person.image_path.split('\\')[-1] }}"
                                             alt="{{ person.name }}"
                                             class="rounded-circle"
                                             style="width: 40px; height: 40px; object-fit: cover;"
                                             onerror="this.style.display='none'; this.nextElementSibling.style.display='flex';">
                                        <div class="bg-primary text-white rounded-circle d-flex align-items-center justify-content-center"
                                             style="width: 40px; height: 40px; font-size: 18px; display: none;">
                                            {{ person.name[0].upper() }}
                                        </div>
                                    {% else %}
                                        <div class="bg-primary text-white rounded-circle d-flex align-items-center justify-content-center"
                                             style="width: 40px; height: 40px; font-size: 18px;">
                                            {{ person.name[0].upper() }}
                                        </div>
                                    {% endif %}
                                </div>
                            </td>
                            <td><strong>{{ person.name }}</strong></td>
                            <td>{{ person.full_name or '-' }}</td>
                            <td>{{ person.department or '-' }}</td>
                            <td>{{ person.email or '-' }}</td>
                            <td>{{ person.phone or '-' }}</td>
                            <td>{{ person.created_at }}</td>
                            <td>
                                <span class="badge {{ 'bg-success' if person.is_active else 'bg-secondary' }}">
                                    {{ 'نشط' if person.is_active else 'غير نشط' }}
                                </span>
                            </td>
                            <td>
                                <div class="btn-group btn-group-sm">
                                    <button class="btn btn-outline-primary" onclick="editPerson({{ person.id }})" title="تعديل">
                                        <i class="bi bi-pencil"></i>
                                    </button>
                                    <button class="btn btn-outline-danger" onclick="deletePerson({{ person.id }})" title="حذف">
                                        <i class="bi bi-trash"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                        {% endfor %}
                    {% else %}
                        <tr>
                            <td colspan="9" class="text-center text-muted">
                                <i class="bi bi-people me-2"></i>
                                لا توجد أشخاص مسجلون
                                <br>
                                <button class="btn btn-primary btn-sm mt-2" onclick="showAddModal()">
                                    <i class="bi bi-person-plus"></i> إضافة أول شخص
                                </button>
                            </td>
                        </tr>
                    {% endif %}
                </tbody>
            </table>
        </div>
    </div>
</div>

<!-- Add Person Modal -->
<div class="modal fade" id="addPersonModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title"><i class="bi bi-person-plus me-2"></i>إضافة شخص جديد</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="add-person-form">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="person-name" class="form-label">الاسم المختصر *</label>
                                <input type="text" class="form-control" id="person-name" required>
                                <div class="form-text">سيتم استخدامه في التعرف</div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="person-full-name" class="form-label">الاسم الكامل</label>
                                <input type="text" class="form-control" id="person-full-name">
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="person-email" class="form-label">البريد الإلكتروني</label>
                                <input type="email" class="form-control" id="person-email">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="person-phone" class="form-label">رقم الهاتف</label>
                                <input type="tel" class="form-control" id="person-phone">
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="person-department" class="form-label">القسم</label>
                                <input type="text" class="form-control" id="person-department">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="person-position" class="form-label">المنصب</label>
                                <input type="text" class="form-control" id="person-position">
                            </div>
                        </div>
                    </div>
                    <div class="mb-3">
                        <label for="person-notes" class="form-label">ملاحظات</label>
                        <textarea class="form-control" id="person-notes" rows="3"></textarea>
                    </div>
                    <div class="mb-3">
                        <label for="person-image" class="form-label">صورة الشخص</label>
                        <input type="file" class="form-control" id="person-image" accept="image/*" onchange="previewImage(this)">
                        <div class="form-text">اختر صورة واضحة للوجه (JPG, PNG)</div>
                        <div id="image-preview" class="mt-2" style="display: none;">
                            <img id="preview-img" src="" alt="معاينة الصورة" class="img-thumbnail" style="max-width: 200px; max-height: 200px;">
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <button type="button" class="btn btn-primary" id="save-person-btn" onclick="savePerson()">
                    <span id="save-spinner" class="spinner-border spinner-border-sm me-2" role="status" style="display: none;"></span>
                    حفظ
                </button>
            </div>
        </div>
    </div>
</div>

<script>
// متغيرات عامة
var personsData = [];
var filteredPersons = [];

// دوال مساعدة
function formatDateTime(dateString) {
    if (!dateString) return '-';
    try {
        var date = new Date(dateString);
        return date.toLocaleDateString('ar-SA') + ' ' + date.toLocaleTimeString('ar-SA', {hour: '2-digit', minute: '2-digit'});
    } catch (e) {
        return dateString;
    }
}

function previewImage(input) {
    var preview = document.getElementById('image-preview');
    var previewImg = document.getElementById('preview-img');
    
    if (input.files && input.files[0]) {
        var reader = new FileReader();
        reader.onload = function(e) {
            previewImg.src = e.target.result;
            preview.style.display = 'block';
        };
        reader.readAsDataURL(input.files[0]);
    } else {
        preview.style.display = 'none';
    }
}

function showAddModal() {
    var modal = new bootstrap.Modal(document.getElementById('addPersonModal'));
    modal.show();
}

// تحديث القائمة من الخادم
function loadPersonsNow() {
    console.log('🔄 إعادة تحميل الصفحة...');
    window.location.reload();
}

// فلترة الأشخاص
function filterPersons() {
    var searchTerm = document.getElementById('search-input').value.toLowerCase();
    var departmentFilter = document.getElementById('department-filter').value;
    var statusFilter = document.getElementById('status-filter').value;

    var rows = document.querySelectorAll('#persons-table tr');
    var visibleCount = 0;

    for (var i = 1; i < rows.length; i++) { // تخطي header
        var row = rows[i];
        var cells = row.querySelectorAll('td');

        if (cells.length < 8) continue; // تأكد من وجود جميع الخلايا

        var name = cells[1].textContent.toLowerCase();
        var fullName = cells[2].textContent.toLowerCase();
        var department = cells[3].textContent.trim();
        var email = cells[4].textContent.toLowerCase();
        var status = cells[7].textContent.trim();

        var matchesSearch = !searchTerm ||
            name.indexOf(searchTerm) !== -1 ||
            fullName.indexOf(searchTerm) !== -1 ||
            email.indexOf(searchTerm) !== -1;

        var matchesDepartment = !departmentFilter || department === departmentFilter;

        var matchesStatus = !statusFilter ||
            (statusFilter === 'active' && status === 'نشط') ||
            (statusFilter === 'inactive' && status === 'غير نشط');

        if (matchesSearch && matchesDepartment && matchesStatus) {
            row.style.display = '';
            visibleCount++;
        } else {
            row.style.display = 'none';
        }
    }

    console.log('🔍 تم فلترة النتائج:', visibleCount, 'من أصل', rows.length - 1);
}

function editPerson(personId) {
    showAlert('ميزة التعديل ستكون متاحة قريباً', 'info');
}

function deletePerson(personId) {
    if (confirm('هل أنت متأكد من حذف هذا الشخص؟')) {
        showAlert('ميزة الحذف ستكون متاحة قريباً', 'info');
    }
}

function testAPI() {
    console.log('🧪 اختبار API...');

    var xhr = new XMLHttpRequest();
    xhr.open('GET', '/api/persons', true);

    xhr.onreadystatechange = function() {
        if (xhr.readyState === 4) {
            console.log('📡 نتيجة الاختبار:', xhr.status, xhr.statusText);
            console.log('📄 محتوى الاستجابة:', xhr.responseText.substring(0, 500));

            if (xhr.status === 200) {
                try {
                    var data = JSON.parse(xhr.responseText);
                    alert('✅ API يعمل بشكل صحيح!\nعدد الأشخاص: ' + data.length);
                } catch (e) {
                    alert('❌ خطأ في تحليل JSON: ' + e.message);
                }
            } else {
                alert('❌ خطأ في API: ' + xhr.status + ' ' + xhr.statusText);
            }
        }
    };

    xhr.send();
}

// تحميل تلقائي عند تحميل الصفحة
console.log('✅ تم تحميل صفحة الأشخاص');
console.log('📊 عدد الأشخاص المعروضين:', document.querySelectorAll('#persons-table tr').length - 1);

// إعداد الفلاتر
populateFiltersFromDOM();

function populateFiltersFromDOM() {
    var departments = [];
    var rows = document.querySelectorAll('#persons-table tr');

    for (var i = 1; i < rows.length; i++) { // تخطي header
        var cells = rows[i].querySelectorAll('td');
        if (cells.length > 3) {
            var dept = cells[3].textContent.trim();
            if (dept && dept !== '-' && departments.indexOf(dept) === -1) {
                departments.push(dept);
            }
        }
    }

    var departmentFilter = document.getElementById('department-filter');
    var html = '<option value="">جميع الأقسام</option>';
    for (var i = 0; i < departments.length; i++) {
        html += '<option value="' + departments[i] + '">' + departments[i] + '</option>';
    }
    departmentFilter.innerHTML = html;
}
</script>
{% endblock %}
