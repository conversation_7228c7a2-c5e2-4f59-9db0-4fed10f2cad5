// إجبار تحديث الصفحة لإظهار التحديثات الجديدة
console.log('🔄 تم تحميل ملف التحديث الجديد - الزر الإداري متاح الآن!');

// التحقق من وجود الزر الإداري
document.addEventListener('DOMContentLoaded', function() {
    const adminBtn = document.querySelector('.admin-control-btn');
    const adminLink = document.querySelector('a[href="/admin"]');
    
    if (adminBtn) {
        console.log('✅ تم العثور على الزر الإداري المميز');
    } else if (adminLink) {
        console.log('✅ تم العثور على رابط لوحة التحكم الإدارية');
    } else {
        console.log('❌ لم يتم العثور على الزر الإداري - قد تحتاج لمسح cache المتصفح');
    }
    
    // إضافة تأثير بصري للتأكيد
    const allAdminLinks = document.querySelectorAll('a[href="/admin"]');
    allAdminLinks.forEach(link => {
        link.style.border = '2px solid #ffd700';
        link.style.boxShadow = '0 0 10px rgba(255, 215, 0, 0.5)';
        setTimeout(() => {
            link.style.border = '';
            link.style.boxShadow = '';
        }, 3000);
    });
});
