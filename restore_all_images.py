#!/usr/bin/env python3
"""
استعادة جميع الصور الشخصية وربطها بأصحابها
"""

import sys
import os
import shutil

# إضافة مسار المشروع إلى Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from src.database.database_manager import DatabaseManager
from src.config.settings import Settings

def restore_all_images():
    """استعادة جميع الصور الشخصية"""
    try:
        settings = Settings()
        db = DatabaseManager(settings.database.database_path)
        persons = db.get_all_persons()
        
        persons_dir = os.path.join(os.getcwd(), 'data', 'persons')
        static_dir = os.path.join(os.getcwd(), 'src', 'web', 'static', 'persons')
        
        # إنشاء مجلد static إذا لم يكن موجوداً
        os.makedirs(static_dir, exist_ok=True)
        
        print("استعادة جميع الصور الشخصية...")
        print("-" * 50)
        
        # البحث عن جميع الصور في مجلد data/persons
        all_images = []
        if os.path.exists(persons_dir):
            for file in os.listdir(persons_dir):
                if file.lower().endswith(('.jpg', '.jpeg', '.png', '.bmp', '.gif')):
                    all_images.append(file)
        
        print(f"الصور الموجودة في data/persons: {len(all_images)}")
        for img in all_images:
            print(f"  - {img}")
        print("-" * 30)
        
        updated_count = 0
        
        for person in persons:
            name = person.get('name', '')
            person_id = person.get('id')
            current_image = person.get('image_path', '')
            
            print(f"\nمعالجة: {name}")
            print(f"الصورة الحالية: {current_image}")
            
            # البحث عن أفضل صورة لهذا الشخص
            best_image = None
            
            # 1. البحث عن صورة تحمل اسم الشخص بالضبط
            for img in all_images:
                img_name = os.path.splitext(img)[0].lower()
                person_name = name.lower()
                
                # مطابقة دقيقة
                if img_name == person_name:
                    best_image = img
                    break
                # مطابقة مع مسافات
                elif img_name.replace(' ', '') == person_name.replace(' ', ''):
                    best_image = img
                    break
                # مطابقة مع underscore
                elif img_name.replace('_', ' ') == person_name:
                    best_image = img
                    break
            
            # 2. إذا لم نجد مطابقة دقيقة، ابحث عن مطابقة جزئية
            if not best_image:
                for img in all_images:
                    img_name = os.path.splitext(img)[0].lower()
                    person_name = name.lower()
                    
                    # إذا كان اسم الشخص يحتوي في اسم الصورة
                    if person_name in img_name or img_name in person_name:
                        # تجنب الصور الافتراضية إلا إذا لم نجد بديل
                        if '_default' not in img_name:
                            best_image = img
                            break
            
            # 3. إذا لم نجد أي مطابقة، ابحث عن صورة افتراضية
            if not best_image:
                for img in all_images:
                    if f"{name}_default" in img:
                        best_image = img
                        break
            
            if best_image:
                # تحديث مسار الصورة في قاعدة البيانات
                full_path = os.path.join(persons_dir, best_image)
                
                # نسخ الصورة إلى مجلد static
                static_path = os.path.join(static_dir, best_image)
                if os.path.exists(full_path):
                    shutil.copy2(full_path, static_path)
                    print(f"📁 تم نسخ الصورة إلى static: {best_image}")
                
                # تحديث قاعدة البيانات
                db.update_person(person_id, image_path=full_path)
                updated_count += 1
                
                # تحديد نوع الصورة
                if '_default' in best_image:
                    print(f"🎨 تم ربط صورة افتراضية: {best_image}")
                else:
                    print(f"📸 تم ربط صورة حقيقية: {best_image}")
            else:
                print(f"❌ لم يتم العثور على أي صورة")
        
        print("-" * 50)
        print(f"✅ تم تحديث {updated_count} شخص")
        
        # عرض النتائج النهائية
        print("\nالنتائج النهائية:")
        persons = db.get_all_persons()  # إعادة تحميل البيانات
        
        real_count = 0
        default_count = 0
        no_image_count = 0
        
        for person in persons:
            name = person.get('name')
            image_path = person.get('image_path', '')
            
            if image_path:
                filename = os.path.basename(image_path)
                if '_default' in filename:
                    print(f"🎨 {name}: {filename} (صورة افتراضية)")
                    default_count += 1
                else:
                    print(f"📸 {name}: {filename} (صورة حقيقية)")
                    real_count += 1
            else:
                print(f"❌ {name}: لا توجد صورة")
                no_image_count += 1
        
        print("-" * 50)
        print(f"📊 الإحصائيات:")
        print(f"   📸 صور حقيقية: {real_count}")
        print(f"   🎨 صور افتراضية: {default_count}")
        print(f"   ❌ بدون صورة: {no_image_count}")
        print(f"   📋 المجموع: {len(persons)}")
                
    except Exception as e:
        print(f"خطأ: {e}")

if __name__ == "__main__":
    restore_all_images()
