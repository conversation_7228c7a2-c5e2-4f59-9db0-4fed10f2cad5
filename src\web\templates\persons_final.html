{% extends "base.html" %}

{% block title %}إدارة الأشخاص - نظام التعرف على الوجوه{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h2><i class="bi bi-people me-2"></i>إدارة الأشخاص</h2>
    <div>
        <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addPersonModal">
            <i class="bi bi-person-plus"></i> إضافة شخص جديد
        </button>
        <a href="/persons" class="btn btn-secondary ms-2">
            <i class="bi bi-arrow-clockwise"></i> تحديث القائمة
        </a>
    </div>
</div>

<!-- Search and Filter -->
<div class="card mb-4">
    <div class="card-body">
        <div class="row">
            <div class="col-md-6">
                <div class="input-group">
                    <span class="input-group-text"><i class="bi bi-search"></i></span>
                    <input type="text" class="form-control" id="search-input" placeholder="البحث عن شخص..." onkeyup="filterTable()">
                </div>
            </div>
            <div class="col-md-3">
                <select class="form-select" id="department-filter" onchange="filterTable()">
                    <option value="">جميع الأقسام</option>
                    {% set departments = [] %}
                    {% for person in persons %}
                        {% if person.department and person.department not in departments %}
                            {% set _ = departments.append(person.department) %}
                        {% endif %}
                    {% endfor %}
                    {% for dept in departments %}
                        <option value="{{ dept }}">{{ dept }}</option>
                    {% endfor %}
                </select>
            </div>
            <div class="col-md-3">
                <select class="form-select" id="status-filter" onchange="filterTable()">
                    <option value="">جميع الحالات</option>
                    <option value="active">نشط</option>
                    <option value="inactive">غير نشط</option>
                </select>
            </div>
        </div>
    </div>
</div>

<!-- Persons Table -->
<div class="card">
    <div class="card-header">
        <h5 class="mb-0"><i class="bi bi-table me-2"></i>قائمة الأشخاص ({{ persons|length }})</h5>
    </div>
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-hover table-custom" id="persons-table">
                <thead class="table-dark">
                    <tr>
                        <th>الصورة</th>
                        <th>الاسم</th>
                        <th>الاسم الكامل</th>
                        <th>القسم</th>
                        <th>البريد الإلكتروني</th>
                        <th>الهاتف</th>
                        <th>تاريخ الإضافة</th>
                        <th>الحالة</th>
                        <th>الإجراءات</th>
                    </tr>
                </thead>
                <tbody>
                    {% if persons %}
                        {% for person in persons %}
                        <tr data-name="{{ person.name|lower }}" data-fullname="{{ person.full_name|lower if person.full_name else '' }}" data-email="{{ person.email|lower if person.email else '' }}" data-department="{{ person.department if person.department else '' }}" data-status="{{ 'active' if person.is_active else 'inactive' }}">
                            <td>
                                <div class="d-flex align-items-center justify-content-center">
                                    {% if person.image_path %}
                                        <img src="/static/persons/{{ person.image_path.split('\\')[-1] }}" 
                                             alt="{{ person.name }}" 
                                             class="rounded-circle" 
                                             style="width: 40px; height: 40px; object-fit: cover;"
                                             onerror="this.style.display='none'; this.nextElementSibling.style.display='flex';">
                                        <div class="bg-primary text-white rounded-circle d-flex align-items-center justify-content-center" 
                                             style="width: 40px; height: 40px; font-size: 18px; display: none;">
                                            {{ person.name[0].upper() }}
                                        </div>
                                    {% else %}
                                        <div class="bg-primary text-white rounded-circle d-flex align-items-center justify-content-center" 
                                             style="width: 40px; height: 40px; font-size: 18px;">
                                            {{ person.name[0].upper() }}
                                        </div>
                                    {% endif %}
                                </div>
                            </td>
                            <td><strong>{{ person.name }}</strong></td>
                            <td>{{ person.full_name or '-' }}</td>
                            <td>{{ person.department or '-' }}</td>
                            <td>{{ person.email or '-' }}</td>
                            <td>{{ person.phone or '-' }}</td>
                            <td>{{ person.created_at }}</td>
                            <td>
                                <span class="badge {{ 'bg-success' if person.is_active else 'bg-secondary' }}">
                                    {{ 'نشط' if person.is_active else 'غير نشط' }}
                                </span>
                            </td>
                            <td>
                                <div class="btn-group btn-group-sm">
                                    <button class="btn btn-outline-primary" onclick="editPerson({{ person.id }})" title="تعديل">
                                        <i class="bi bi-pencil"></i>
                                    </button>
                                    <button class="btn btn-outline-danger" onclick="deletePerson({{ person.id }})" title="حذف">
                                        <i class="bi bi-trash"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                        {% endfor %}
                    {% else %}
                        <tr>
                            <td colspan="9" class="text-center text-muted py-5">
                                <i class="bi bi-people display-1 text-muted"></i>
                                <h4 class="mt-3">لا توجد أشخاص مسجلون</h4>
                                <p class="text-muted">ابدأ بإضافة أول شخص إلى النظام</p>
                                <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addPersonModal">
                                    <i class="bi bi-person-plus"></i> إضافة أول شخص
                                </button>
                            </td>
                        </tr>
                    {% endif %}
                </tbody>
            </table>
        </div>
    </div>
</div>

<!-- Add Person Modal -->
<div class="modal fade" id="addPersonModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title"><i class="bi bi-person-plus me-2"></i>إضافة شخص جديد</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="add-person-form" enctype="multipart/form-data" action="/api/persons" method="post">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="person-name" class="form-label">الاسم المختصر *</label>
                                <input type="text" class="form-control" id="person-name" name="name" required>
                                <div class="form-text">سيتم استخدامه في التعرف</div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="person-full-name" class="form-label">الاسم الكامل</label>
                                <input type="text" class="form-control" id="person-full-name" name="full_name">
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="person-email" class="form-label">البريد الإلكتروني</label>
                                <input type="email" class="form-control" id="person-email" name="email">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="person-phone" class="form-label">رقم الهاتف</label>
                                <input type="tel" class="form-control" id="person-phone" name="phone">
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="person-department" class="form-label">القسم</label>
                                <input type="text" class="form-control" id="person-department" name="department">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="person-position" class="form-label">المنصب</label>
                                <input type="text" class="form-control" id="person-position" name="position">
                            </div>
                        </div>
                    </div>
                    <div class="mb-3">
                        <label for="person-notes" class="form-label">ملاحظات</label>
                        <textarea class="form-control" id="person-notes" name="notes" rows="3"></textarea>
                    </div>
                    <div class="mb-3">
                        <label for="person-image" class="form-label">صورة الشخص</label>
                        <input type="file" class="form-control" id="person-image" name="image" accept="image/*" onchange="previewImage(this)">
                        <div class="form-text">اختر صورة واضحة للوجه (JPG, PNG)</div>
                        <div id="image-preview" class="mt-2" style="display: none;">
                            <img id="preview-img" src="" alt="معاينة الصورة" class="img-thumbnail" style="max-width: 200px; max-height: 200px;">
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <button type="button" class="btn btn-primary" onclick="submitForm()">
                    <span id="save-spinner" class="spinner-border spinner-border-sm me-2" role="status" style="display: none;"></span>
                    حفظ
                </button>
            </div>
        </div>
    </div>
</div>

<script>
// معاينة الصورة
function previewImage(input) {
    var preview = document.getElementById('image-preview');
    var previewImg = document.getElementById('preview-img');
    
    if (input.files && input.files[0]) {
        var reader = new FileReader();
        reader.onload = function(e) {
            previewImg.src = e.target.result;
            preview.style.display = 'block';
        };
        reader.readAsDataURL(input.files[0]);
    } else {
        preview.style.display = 'none';
    }
}

// فلترة الجدول
function filterTable() {
    var searchTerm = document.getElementById('search-input').value.toLowerCase();
    var departmentFilter = document.getElementById('department-filter').value;
    var statusFilter = document.getElementById('status-filter').value;
    
    var rows = document.querySelectorAll('#persons-table tbody tr');
    var visibleCount = 0;
    
    for (var i = 0; i < rows.length; i++) {
        var row = rows[i];
        
        // تخطي الصف إذا كان يحتوي على رسالة "لا توجد أشخاص"
        if (row.querySelector('td[colspan]')) {
            continue;
        }
        
        var name = row.getAttribute('data-name') || '';
        var fullname = row.getAttribute('data-fullname') || '';
        var email = row.getAttribute('data-email') || '';
        var department = row.getAttribute('data-department') || '';
        var status = row.getAttribute('data-status') || '';
        
        var matchesSearch = !searchTerm || 
            name.indexOf(searchTerm) !== -1 ||
            fullname.indexOf(searchTerm) !== -1 ||
            email.indexOf(searchTerm) !== -1;
        
        var matchesDepartment = !departmentFilter || department === departmentFilter;
        var matchesStatus = !statusFilter || status === statusFilter;
        
        if (matchesSearch && matchesDepartment && matchesStatus) {
            row.style.display = '';
            visibleCount++;
        } else {
            row.style.display = 'none';
        }
    }
    
    console.log('🔍 تم فلترة النتائج:', visibleCount, 'من أصل', rows.length);
}

// إرسال النموذج
function submitForm() {
    var form = document.getElementById('add-person-form');
    var spinner = document.getElementById('save-spinner');
    var submitBtn = document.querySelector('#addPersonModal .btn-primary');
    
    // التحقق من البيانات المطلوبة
    var name = document.getElementById('person-name').value.trim();
    if (!name) {
        showAlert('الاسم مطلوب', 'warning');
        return;
    }
    
    // عرض مؤشر التحميل
    spinner.style.display = 'inline-block';
    submitBtn.disabled = true;
    
    // إنشاء FormData
    var formData = new FormData(form);
    
    // إرسال البيانات
    fetch('/api/persons', {
        method: 'POST',
        body: formData
    })
    .then(function(response) {
        return response.json().then(function(data) {
            return { status: response.status, data: data };
        });
    })
    .then(function(result) {
        if (result.status === 200 || result.status === 201) {
            showAlert(result.data.message || 'تم إضافة الشخص بنجاح', 'success');
            // إغلاق النافذة وإعادة تحميل الصفحة
            var modal = bootstrap.Modal.getInstance(document.getElementById('addPersonModal'));
            modal.hide();
            setTimeout(function() {
                window.location.reload();
            }, 1000);
        } else {
            showAlert(result.data.error || 'فشل في إضافة الشخص', 'danger');
        }
    })
    .catch(function(error) {
        console.error('Error:', error);
        showAlert('فشل في إضافة الشخص', 'danger');
    })
    .finally(function() {
        spinner.style.display = 'none';
        submitBtn.disabled = false;
    });
}

function editPerson(personId) {
    showAlert('ميزة التعديل ستكون متاحة قريباً', 'info');
}

function deletePerson(personId) {
    if (confirm('هل أنت متأكد من حذف هذا الشخص؟')) {
        showAlert('ميزة الحذف ستكون متاحة قريباً', 'info');
    }
}

// تسجيل تحميل الصفحة
console.log('✅ تم تحميل صفحة الأشخاص بنجاح');
console.log('📊 عدد الأشخاص المعروضين:', {{ persons|length }});
</script>
{% endblock %}
