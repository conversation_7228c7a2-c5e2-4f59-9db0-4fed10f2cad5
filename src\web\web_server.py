"""
خادم الويب للتحكم في نظام التعرف على الوجوه
Web Server for Face Recognition System Control
"""

from flask import Flask, render_template, request, jsonify, send_file
from flask_cors import CORS
from flask_socketio import Socket<PERSON>, emit
import json
import base64
import cv2
import numpy as np
from datetime import datetime
from pathlib import Path
import threading
import time

from ..config.settings import Settings
from ..database.database_manager import DatabaseManager
from ..utils.logger import get_logger


class WebServer:
    """خادم الويب للتحكم في النظام"""
    
    def __init__(self, settings: Settings, db: DatabaseManager, face_system=None):
        self.settings = settings
        self.db = db
        self.face_system = face_system
        self.logger = get_logger()
        
        # إعداد Flask
        self.app = Flask(__name__, 
                        template_folder='templates',
                        static_folder='static')
        self.app.config['SECRET_KEY'] = 'face_recognition_secret_key_2025'
        
        # إعداد CORS
        CORS(self.app)
        
        # إعداد SocketIO
        self.socketio = SocketIO(self.app, cors_allowed_origins="*")
        
        # حالة الخادم
        self.is_running = False
        self.connected_clients = set()
        
        # إعداد المسارات
        self._setup_routes()
        self._setup_socketio_events()
        
        self.logger.info("تم تهيئة خادم الويب")
    
    def _setup_routes(self):
        """إعداد مسارات الويب"""
        
        @self.app.route('/')
        def index():
            """الصفحة الرئيسية"""
            return render_template('index.html')
        
        @self.app.route('/dashboard')
        def dashboard():
            """لوحة التحكم"""
            return render_template('dashboard.html')
        
        @self.app.route('/persons')
        def persons():
            """صفحة إدارة الأشخاص"""
            return render_template('persons.html')
        
        @self.app.route('/settings')
        def settings():
            """صفحة الإعدادات"""
            return render_template('settings.html')
        
        @self.app.route('/logs')
        def logs():
            """صفحة السجلات"""
            return render_template('logs.html')
        
        # API Routes
        @self.app.route('/api/status')
        def api_status():
            """حالة النظام"""
            try:
                status = {
                    'system_running': self.face_system.is_running if self.face_system else False,
                    'camera_active': True,  # يمكن تحسينها
                    'database_connected': True,
                    'timestamp': datetime.now().isoformat()
                }
                return jsonify(status)
            except Exception as e:
                return jsonify({'error': str(e)}), 500
        
        @self.app.route('/api/statistics')
        def api_statistics():
            """إحصائيات النظام"""
            try:
                # إحصائيات الأداء
                performance_stats = {}
                if self.face_system and hasattr(self.face_system, 'performance_monitor'):
                    performance_stats = self.face_system.performance_monitor.get_session_statistics()
                
                # إحصائيات الأمان
                security_stats = {}
                if self.face_system and hasattr(self.face_system, 'security_manager'):
                    security_stats = self.face_system.security_manager.get_security_statistics()
                
                # إحصائيات قاعدة البيانات
                db_info = self.db.get_database_info() if self.db else {}
                
                return jsonify({
                    'performance': performance_stats,
                    'security': security_stats,
                    'database': db_info,
                    'timestamp': datetime.now().isoformat()
                })
            except Exception as e:
                return jsonify({'error': str(e)}), 500
        
        @self.app.route('/api/persons')
        def api_persons():
            """قائمة الأشخاص"""
            try:
                persons = self.db.get_all_persons() if self.db else []
                return jsonify(persons)
            except Exception as e:
                return jsonify({'error': str(e)}), 500
        
        @self.app.route('/api/persons', methods=['POST'])
        def api_add_person():
            """إضافة شخص جديد"""
            try:
                data = request.get_json()
                name = data.get('name')
                full_name = data.get('full_name', name)
                email = data.get('email', '')
                phone = data.get('phone', '')
                department = data.get('department', '')
                
                if not name:
                    return jsonify({'error': 'الاسم مطلوب'}), 400
                
                person_id = self.db.add_person(
                    name=name,
                    full_name=full_name,
                    email=email,
                    phone=phone,
                    department=department
                )
                
                return jsonify({'id': person_id, 'message': 'تم إضافة الشخص بنجاح'})
            except Exception as e:
                return jsonify({'error': str(e)}), 500
        
        @self.app.route('/api/persons/<int:person_id>', methods=['PUT'])
        def api_update_person(person_id):
            """تحديث بيانات شخص"""
            try:
                data = request.get_json()
                success = self.db.update_person(person_id, **data)
                
                if success:
                    return jsonify({'message': 'تم تحديث البيانات بنجاح'})
                else:
                    return jsonify({'error': 'فشل في تحديث البيانات'}), 400
            except Exception as e:
                return jsonify({'error': str(e)}), 500
        
        @self.app.route('/api/persons/<int:person_id>', methods=['DELETE'])
        def api_delete_person(person_id):
            """حذف شخص"""
            try:
                success = self.db.delete_person(person_id)
                
                if success:
                    return jsonify({'message': 'تم حذف الشخص بنجاح'})
                else:
                    return jsonify({'error': 'فشل في حذف الشخص'}), 400
            except Exception as e:
                return jsonify({'error': str(e)}), 500
        
        @self.app.route('/api/recognition-log')
        def api_recognition_log():
            """سجل التعرف"""
            try:
                days = request.args.get('days', 7, type=int)
                logs = self.db.get_recognition_history(days=days) if self.db else []
                return jsonify(logs)
            except Exception as e:
                return jsonify({'error': str(e)}), 500
        
        @self.app.route('/api/settings', methods=['GET'])
        def api_get_settings():
            """الحصول على الإعدادات"""
            try:
                settings_dict = {
                    'camera': self.settings.camera.__dict__,
                    'recognition': self.settings.recognition.__dict__,
                    'ui': self.settings.ui.__dict__,
                    'security': self.settings.security.__dict__,
                    'performance': self.settings.performance.__dict__
                }
                return jsonify(settings_dict)
            except Exception as e:
                return jsonify({'error': str(e)}), 500
        
        @self.app.route('/api/settings', methods=['POST'])
        def api_update_settings():
            """تحديث الإعدادات"""
            try:
                data = request.get_json()
                
                # تحديث الإعدادات
                for category, values in data.items():
                    if hasattr(self.settings, category):
                        category_obj = getattr(self.settings, category)
                        for key, value in values.items():
                            if hasattr(category_obj, key):
                                setattr(category_obj, key, value)
                
                # حفظ الإعدادات
                self.settings.save()
                
                return jsonify({'message': 'تم تحديث الإعدادات بنجاح'})
            except Exception as e:
                return jsonify({'error': str(e)}), 500
        
        @self.app.route('/api/system/start', methods=['POST'])
        def api_start_system():
            """بدء تشغيل النظام"""
            try:
                if self.face_system and not self.face_system.is_running:
                    # بدء النظام في خيط منفصل
                    threading.Thread(target=self.face_system.run, daemon=True).start()
                    return jsonify({'message': 'تم بدء تشغيل النظام'})
                else:
                    return jsonify({'error': 'النظام يعمل بالفعل'}), 400
            except Exception as e:
                return jsonify({'error': str(e)}), 500
        
        @self.app.route('/api/system/stop', methods=['POST'])
        def api_stop_system():
            """إيقاف النظام"""
            try:
                if self.face_system and self.face_system.is_running:
                    self.face_system.is_running = False
                    return jsonify({'message': 'تم إيقاف النظام'})
                else:
                    return jsonify({'error': 'النظام متوقف بالفعل'}), 400
            except Exception as e:
                return jsonify({'error': str(e)}), 500
        
        @self.app.route('/api/security/report')
        def api_security_report():
            """تقرير الأمان"""
            try:
                if self.face_system and hasattr(self.face_system, 'security_manager'):
                    report = self.face_system.security_manager.generate_security_report()
                    return jsonify(report)
                else:
                    return jsonify({'error': 'نظام الأمان غير متاح'}), 400
            except Exception as e:
                return jsonify({'error': str(e)}), 500

    def _setup_socketio_events(self):
        """إعداد أحداث SocketIO"""
        
        @self.socketio.on('connect')
        def handle_connect():
            """اتصال عميل جديد"""
            self.connected_clients.add(request.sid)
            self.logger.info(f"عميل جديد متصل: {request.sid}")
            emit('status', {'message': 'متصل بنجاح'})
        
        @self.socketio.on('disconnect')
        def handle_disconnect():
            """قطع اتصال عميل"""
            self.connected_clients.discard(request.sid)
            self.logger.info(f"تم قطع اتصال العميل: {request.sid}")
        
        @self.socketio.on('request_live_data')
        def handle_live_data_request():
            """طلب البيانات المباشرة"""
            self._send_live_data()
    
    def _send_live_data(self):
        """إرسال البيانات المباشرة للعملاء"""
        try:
            if not self.connected_clients:
                return
            
            # جمع البيانات المباشرة
            live_data = {
                'timestamp': datetime.now().isoformat(),
                'system_status': {
                    'running': self.face_system.is_running if self.face_system else False,
                    'fps': 0,
                    'faces_detected': 0
                }
            }
            
            # إضافة بيانات الأداء
            if self.face_system and hasattr(self.face_system, 'performance_monitor'):
                latest_metrics = self.face_system.performance_monitor.get_latest_metrics()
                if latest_metrics:
                    live_data['system_status']['fps'] = latest_metrics.fps
                    live_data['system_status']['faces_detected'] = latest_metrics.faces_detected
            
            # إرسال للعملاء المتصلين
            self.socketio.emit('live_data', live_data)
            
        except Exception as e:
            self.logger.error(f"خطأ في إرسال البيانات المباشرة: {e}")
    
    def start_server(self, host='0.0.0.0', port=5000, debug=False):
        """بدء تشغيل الخادم"""
        try:
            self.is_running = True
            
            # بدء خيط إرسال البيانات المباشرة
            threading.Thread(target=self._live_data_sender, daemon=True).start()
            
            self.logger.info(f"بدء تشغيل خادم الويب على {host}:{port}")
            self.socketio.run(self.app, host=host, port=port, debug=debug)
            
        except Exception as e:
            self.logger.error(f"خطأ في تشغيل خادم الويب: {e}")
    
    def _live_data_sender(self):
        """خيط إرسال البيانات المباشرة"""
        while self.is_running:
            try:
                self._send_live_data()
                time.sleep(1)  # إرسال كل ثانية
            except Exception as e:
                self.logger.error(f"خطأ في خيط البيانات المباشرة: {e}")
                time.sleep(5)
    
    def stop_server(self):
        """إيقاف الخادم"""
        self.is_running = False
        self.logger.info("تم إيقاف خادم الويب")
