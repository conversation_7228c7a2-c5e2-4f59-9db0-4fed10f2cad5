"""
خادم الويب للتحكم في نظام التعرف على الوجوه
Web Server for Face Recognition System Control
"""

from flask import Flask, render_template, request, jsonify, send_file, session, redirect, url_for, flash
from flask_cors import CORS
from flask_socketio import Socket<PERSON>, emit
from werkzeug.utils import secure_filename
import json
import base64
import cv2
import numpy as np
from datetime import datetime
from pathlib import Path
import threading
import time

from ..config.settings import Settings
from ..database.database_manager import DatabaseManager
from ..utils.logger import get_logger
from ..auth.auth_manager import AuthManager, login_required, admin_required


class WebServer:
    """خادم الويب للتحكم في النظام"""
    
    def __init__(self, settings: Settings, db: DatabaseManager, face_system=None):
        self.settings = settings
        self.db = db
        self.face_system = face_system
        self.logger = get_logger()

        # إنشاء نظام المصادقة
        self.auth = AuthManager(self.db, self.logger)
        
        # إعداد Flask
        self.app = Flask(__name__, 
                        template_folder='templates',
                        static_folder='static')
        self.app.config['SECRET_KEY'] = 'face_recognition_secret_key_2025'
        
        # إعداد CORS
        CORS(self.app)
        
        # إعداد SocketIO
        self.socketio = SocketIO(self.app, cors_allowed_origins="*")
        
        # حالة الخادم
        self.is_running = False
        self.connected_clients = set()
        
        # إعداد المسارات
        self._setup_routes()
        self._setup_socketio_events()
        
        self.logger.info("تم تهيئة خادم الويب")
    
    def _setup_routes(self):
        """إعداد مسارات الويب"""
        
        # ==================== routes المصادقة ====================

        @self.app.route('/login', methods=['GET', 'POST'])
        def login():
            """صفحة تسجيل الدخول"""
            if request.method == 'POST':
                identifier = request.form.get('identifier')
                password = request.form.get('password')

                if not identifier or not password:
                    flash('يرجى إدخال جميع البيانات المطلوبة', 'error')
                    return render_template('login.html')

                # مصادقة المستخدم
                user = self.auth.authenticate_user(identifier, password)

                if user:
                    # إنشاء جلسة
                    self.auth.create_session(user)
                    flash(f'مرحباً {user["full_name"] or user["username"]}', 'success')

                    # توجيه المدير إلى لوحة التحكم الإدارية
                    if user['role'] == 'admin':
                        return redirect(url_for('admin_dashboard'))
                    else:
                        return redirect(url_for('dashboard'))
                else:
                    flash('رقم الهوية أو كلمة المرور غير صحيحة', 'error')

            return render_template('login.html')

        @self.app.route('/logout')
        def logout():
            """تسجيل الخروج"""
            self.auth.destroy_session()
            flash('تم تسجيل الخروج بنجاح', 'info')
            return redirect(url_for('login'))

        @self.app.route('/admin')
        @admin_required
        def admin_dashboard():
            """لوحة التحكم الإدارية"""
            try:
                # جمع الإحصائيات
                stats = {
                    'total_persons': len(self.db.get_all_persons()) if self.db else 0,
                    'active_persons': len([p for p in self.db.get_all_persons() if p.get('is_active', True)]) if self.db else 0,
                    'today_recognitions': 0,  # يمكن تحديثها لاحقاً
                    'total_users': len(self.db.get_all_users()) if self.db else 1
                }

                # النشاط الأخير
                recent_activity = []
                if self.db:
                    try:
                        recent_activity = self.db.get_recent_recognitions(limit=5)
                    except:
                        recent_activity = []

                return render_template('admin_dashboard.html',
                                     current_user=self.auth.get_current_user(),
                                     stats=stats,
                                     recent_activity=recent_activity)
            except Exception as e:
                self.logger.error(f"خطأ في لوحة التحكم الإدارية: {e}")
                flash('خطأ في تحميل لوحة التحكم', 'error')
                return redirect(url_for('login'))

        # ==================== routes الرئيسية ====================

        @self.app.route('/')
        def index():
            """الصفحة الرئيسية"""
            # إعادة توجيه إلى تسجيل الدخول إذا لم يكن مسجل دخول
            if not self.auth.is_logged_in():
                return redirect(url_for('login'))

            # إعادة توجيه حسب نوع المستخدم
            if self.auth.is_admin():
                return redirect(url_for('admin_dashboard'))
            else:
                return redirect(url_for('dashboard'))
        
        @self.app.route('/dashboard')
        @login_required
        def dashboard():
            """لوحة التحكم"""
            return render_template('dashboard.html')
        
        @self.app.route('/persons')
        @login_required
        def persons():
            """صفحة إدارة الأشخاص"""
            try:
                self.logger.info("🔄 بدء تحميل صفحة الأشخاص...")

                # التحقق من قاعدة البيانات
                if not self.db:
                    self.logger.error("❌ قاعدة البيانات غير متاحة")
                    return render_template('persons_final.html', persons=[])

                # تحميل الأشخاص مباشرة من قاعدة البيانات
                self.logger.info("📊 استدعاء get_all_persons من route...")
                persons_list = self.db.get_all_persons()

                self.logger.info(f"✅ تم تحميل صفحة الأشخاص مع {len(persons_list)} شخص")
                self.logger.info(f"📋 أول شخص: {persons_list[0] if persons_list else 'لا يوجد'}")

                return render_template('persons_with_images.html', persons=persons_list)

            except Exception as e:
                self.logger.error(f"❌ خطأ في تحميل صفحة الأشخاص: {e}")
                return render_template('persons_with_images.html', persons=[])
        
        @self.app.route('/settings')
        def settings():
            """صفحة الإعدادات"""
            return render_template('settings.html')

        @self.app.route('/test-persons')
        def test_persons():
            """صفحة اختبار الأشخاص"""
            return render_template('test_persons.html')

        @self.app.route('/persons-simple')
        def persons_simple():
            """صفحة الأشخاص المبسطة"""
            return render_template('persons_simple.html')

        @self.app.route('/static/persons/<filename>')
        def serve_person_image(filename):
            """خدمة صور الأشخاص"""
            try:
                from flask import send_from_directory
                import os

                # البحث في مجلد data/persons أولاً
                persons_dir = os.path.join(os.getcwd(), 'data', 'persons')
                file_path = os.path.join(persons_dir, filename)

                if os.path.exists(file_path):
                    self.logger.info(f"✅ تم العثور على الصورة: {filename}")
                    return send_from_directory(persons_dir, filename)

                # البحث في مجلد static/persons كبديل
                static_persons_dir = os.path.join(os.getcwd(), 'src', 'web', 'static', 'persons')
                static_file_path = os.path.join(static_persons_dir, filename)

                if os.path.exists(static_file_path):
                    self.logger.info(f"✅ تم العثور على الصورة في static: {filename}")
                    return send_from_directory(static_persons_dir, filename)

                self.logger.warning(f"❌ لم يتم العثور على الصورة: {filename}")
                self.logger.warning(f"   - بحث في: {file_path}")
                self.logger.warning(f"   - بحث في: {static_file_path}")
                return '', 404

            except Exception as e:
                self.logger.error(f"❌ خطأ في خدمة الصورة {filename}: {e}")
                return '', 404
        
        @self.app.route('/logs')
        def logs():
            """صفحة السجلات"""
            return render_template('logs.html')

        @self.app.route('/test-images')
        def test_images():
            """صفحة اختبار الصور"""
            try:
                self.logger.info("🔄 بدء تحميل صفحة اختبار الصور...")

                if not self.db:
                    self.logger.error("❌ قاعدة البيانات غير متاحة")
                    return render_template('test_images.html', persons=[])

                persons_list = self.db.get_all_persons()
                self.logger.info(f"✅ تم تحميل صفحة اختبار الصور مع {len(persons_list)} شخص")

                return render_template('test_images.html', persons=persons_list)

            except Exception as e:
                self.logger.error(f"❌ خطأ في تحميل صفحة اختبار الصور: {e}")
                return render_template('test_images.html', persons=[])
        
        # API Routes
        @self.app.route('/api/status')
        def api_status():
            """حالة النظام"""
            try:
                status = {
                    'system_running': self.face_system.is_running if self.face_system else False,
                    'camera_active': True,  # يمكن تحسينها
                    'database_connected': True,
                    'timestamp': datetime.now().isoformat()
                }
                return jsonify(status)
            except Exception as e:
                return jsonify({'error': str(e)}), 500
        
        @self.app.route('/api/statistics')
        def api_statistics():
            """إحصائيات النظام"""
            try:
                # إحصائيات الأداء
                performance_stats = {}
                if self.face_system and hasattr(self.face_system, 'performance_monitor'):
                    performance_stats = self.face_system.performance_monitor.get_session_statistics()
                
                # إحصائيات الأمان
                security_stats = {}
                if self.face_system and hasattr(self.face_system, 'security_manager'):
                    security_stats = self.face_system.security_manager.get_security_statistics()
                
                # إحصائيات قاعدة البيانات
                db_info = self.db.get_database_info() if self.db else {}
                
                return jsonify({
                    'performance': performance_stats,
                    'security': security_stats,
                    'database': db_info,
                    'timestamp': datetime.now().isoformat()
                })
            except Exception as e:
                return jsonify({'error': str(e)}), 500
        
        @self.app.route('/api/persons')
        def api_persons():
            """قائمة الأشخاص"""
            try:
                self.logger.info("طلب قائمة الأشخاص...")

                if not self.db:
                    self.logger.error("قاعدة البيانات غير متاحة")
                    return jsonify({'error': 'قاعدة البيانات غير متاحة'}), 500

                self.logger.info("استدعاء get_all_persons...")
                persons = self.db.get_all_persons()

                self.logger.info(f"نتيجة get_all_persons: {type(persons)}, العدد: {len(persons) if isinstance(persons, list) else 'غير محدد'}")

                # التأكد من أن النتيجة قائمة
                if not isinstance(persons, list):
                    self.logger.warning(f"النتيجة ليست قائمة: {type(persons)}")
                    persons = []

                # طباعة عينة من البيانات
                if persons:
                    self.logger.info(f"أول شخص: {persons[0]}")

                self.logger.info(f"إرسال {len(persons)} شخص إلى العميل")

                response = jsonify(persons)
                response.headers['Access-Control-Allow-Origin'] = '*'
                return response

            except Exception as e:
                self.logger.error(f"خطأ في استرجاع قائمة الأشخاص: {e}", exc_info=True)
                return jsonify({'error': f'فشل في تحميل البيانات: {str(e)}'}), 500
        
        @self.app.route('/api/persons', methods=['POST'])
        def api_add_person():
            """إضافة شخص جديد"""
            try:
                # التحقق من نوع المحتوى
                if request.content_type and 'multipart/form-data' in request.content_type:
                    # رفع ملف مع بيانات
                    name = request.form.get('name')
                    full_name = request.form.get('full_name', name)
                    email = request.form.get('email', '')
                    phone = request.form.get('phone', '')
                    department = request.form.get('department', '')
                    position = request.form.get('position', '')
                    notes = request.form.get('notes', '')

                    # معالجة الصورة
                    image_path = None
                    encoding_data = None

                    if 'image' in request.files:
                        file = request.files['image']
                        if file and file.filename:
                            # حفظ الصورة
                            import os
                            from pathlib import Path
                            import uuid

                            # إنشاء اسم ملف فريد
                            file_extension = Path(file.filename).suffix.lower()
                            if file_extension not in ['.jpg', '.jpeg', '.png']:
                                return jsonify({'error': 'نوع الملف غير مدعوم. استخدم JPG أو PNG'}), 400

                            filename = f"{name}_{uuid.uuid4().hex[:8]}{file_extension}"
                            image_path = Path("data/persons") / filename
                            image_path.parent.mkdir(parents=True, exist_ok=True)

                            file.save(str(image_path))

                            # ترميز الوجه
                            if self.face_system:
                                try:
                                    import cv2
                                    import face_recognition
                                    import pickle

                                    # قراءة الصورة
                                    image = cv2.imread(str(image_path))
                                    if image is not None:
                                        # تحويل إلى RGB
                                        rgb_image = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)

                                        # اكتشاف الوجوه
                                        face_locations = face_recognition.face_locations(rgb_image)
                                        if face_locations:
                                            # ترميز الوجه
                                            face_encodings = face_recognition.face_encodings(rgb_image, face_locations)
                                            if face_encodings:
                                                encoding_data = pickle.dumps(face_encodings[0])
                                        else:
                                            return jsonify({'error': 'لم يتم العثور على وجه في الصورة'}), 400
                                except Exception as e:
                                    self.logger.error(f"خطأ في ترميز الوجه: {e}")
                                    return jsonify({'error': 'فشل في معالجة الصورة'}), 500
                else:
                    # بيانات JSON فقط
                    data = request.get_json()
                    name = data.get('name')
                    full_name = data.get('full_name', name)
                    email = data.get('email', '')
                    phone = data.get('phone', '')
                    department = data.get('department', '')
                    position = data.get('position', '')
                    notes = data.get('notes', '')
                    image_path = None
                    encoding_data = None

                if not name:
                    return jsonify({'error': 'الاسم مطلوب'}), 400

                # التحقق من عدم وجود الاسم مسبقاً
                existing_person = self.db.get_person_by_name(name)
                if existing_person:
                    return jsonify({'error': 'يوجد شخص بهذا الاسم مسبقاً'}), 400

                person_id = self.db.add_person(
                    name=name,
                    full_name=full_name,
                    email=email,
                    phone=phone,
                    department=department,
                    position=position,
                    notes=notes,
                    image_path=str(image_path) if image_path else None,
                    encoding_data=encoding_data
                )

                # إضافة الترميز إلى النظام إذا كان متاحاً
                if encoding_data and self.face_system:
                    try:
                        import pickle
                        encoding = pickle.loads(encoding_data)
                        self.face_system.known_encodings.append(encoding)
                        self.face_system.known_names.append(name)
                        self.logger.info(f"تم إضافة ترميز الوجه للشخص: {name}")
                    except Exception as e:
                        self.logger.error(f"خطأ في إضافة الترميز للنظام: {e}")

                return jsonify({'id': person_id, 'message': 'تم إضافة الشخص بنجاح'})
            except Exception as e:
                self.logger.error(f"خطأ في إضافة شخص: {e}")
                return jsonify({'error': str(e)}), 500
        
        @self.app.route('/api/persons/<int:person_id>', methods=['PUT'])
        def api_update_person(person_id):
            """تحديث بيانات شخص"""
            try:
                data = request.get_json()
                success = self.db.update_person(person_id, **data)
                
                if success:
                    return jsonify({'message': 'تم تحديث البيانات بنجاح'})
                else:
                    return jsonify({'error': 'فشل في تحديث البيانات'}), 400
            except Exception as e:
                return jsonify({'error': str(e)}), 500
        
        @self.app.route('/api/persons/<int:person_id>', methods=['DELETE'])
        def api_delete_person(person_id):
            """حذف شخص"""
            try:
                success = self.db.delete_person(person_id)
                
                if success:
                    return jsonify({'message': 'تم حذف الشخص بنجاح'})
                else:
                    return jsonify({'error': 'فشل في حذف الشخص'}), 400
            except Exception as e:
                return jsonify({'error': str(e)}), 500
        
        @self.app.route('/api/recognition-log')
        def api_recognition_log():
            """سجل التعرف"""
            try:
                days = request.args.get('days', 7, type=int)
                logs = self.db.get_recognition_history(days=days) if self.db else []
                return jsonify(logs)
            except Exception as e:
                return jsonify({'error': str(e)}), 500
        
        @self.app.route('/api/settings', methods=['GET'])
        def api_get_settings():
            """الحصول على الإعدادات"""
            try:
                settings_dict = {
                    'camera': self.settings.camera.__dict__,
                    'recognition': self.settings.recognition.__dict__,
                    'ui': self.settings.ui.__dict__,
                    'security': self.settings.security.__dict__,
                    'performance': self.settings.performance.__dict__
                }
                return jsonify(settings_dict)
            except Exception as e:
                return jsonify({'error': str(e)}), 500
        
        @self.app.route('/api/settings', methods=['POST'])
        def api_update_settings():
            """تحديث الإعدادات"""
            try:
                data = request.get_json()
                
                # تحديث الإعدادات
                for category, values in data.items():
                    if hasattr(self.settings, category):
                        category_obj = getattr(self.settings, category)
                        for key, value in values.items():
                            if hasattr(category_obj, key):
                                setattr(category_obj, key, value)
                
                # حفظ الإعدادات
                self.settings.save()
                
                return jsonify({'message': 'تم تحديث الإعدادات بنجاح'})
            except Exception as e:
                return jsonify({'error': str(e)}), 500
        
        @self.app.route('/api/system/start', methods=['POST'])
        def api_start_system():
            """بدء تشغيل النظام"""
            try:
                if self.face_system and not self.face_system.is_running:
                    # بدء النظام في خيط منفصل
                    threading.Thread(target=self.face_system.run, daemon=True).start()
                    return jsonify({'message': 'تم بدء تشغيل النظام'})
                else:
                    return jsonify({'error': 'النظام يعمل بالفعل'}), 400
            except Exception as e:
                return jsonify({'error': str(e)}), 500
        
        @self.app.route('/api/system/stop', methods=['POST'])
        def api_stop_system():
            """إيقاف النظام"""
            try:
                if self.face_system and self.face_system.is_running:
                    self.face_system.is_running = False
                    return jsonify({'message': 'تم إيقاف النظام'})
                else:
                    return jsonify({'error': 'النظام متوقف بالفعل'}), 400
            except Exception as e:
                return jsonify({'error': str(e)}), 500
        
        @self.app.route('/api/security/report')
        def api_security_report():
            """تقرير الأمان"""
            try:
                if self.face_system and hasattr(self.face_system, 'security_manager'):
                    report = self.face_system.security_manager.generate_security_report()
                    return jsonify(report)
                else:
                    return jsonify({'error': 'نظام الأمان غير متاح'}), 400
            except Exception as e:
                return jsonify({'error': str(e)}), 500

    def _setup_socketio_events(self):
        """إعداد أحداث SocketIO"""
        
        @self.socketio.on('connect')
        def handle_connect():
            """اتصال عميل جديد"""
            self.connected_clients.add(request.sid)
            self.logger.info(f"عميل جديد متصل: {request.sid}")
            emit('status', {'message': 'متصل بنجاح'})
        
        @self.socketio.on('disconnect')
        def handle_disconnect():
            """قطع اتصال عميل"""
            self.connected_clients.discard(request.sid)
            self.logger.info(f"تم قطع اتصال العميل: {request.sid}")
        
        @self.socketio.on('request_live_data')
        def handle_live_data_request():
            """طلب البيانات المباشرة"""
            self._send_live_data()
    
    def _send_live_data(self):
        """إرسال البيانات المباشرة للعملاء"""
        try:
            if not self.connected_clients:
                return
            
            # جمع البيانات المباشرة
            live_data = {
                'timestamp': datetime.now().isoformat(),
                'system_status': {
                    'running': self.face_system.is_running if self.face_system else False,
                    'fps': 0,
                    'faces_detected': 0
                }
            }
            
            # إضافة بيانات الأداء
            if self.face_system and hasattr(self.face_system, 'performance_monitor'):
                latest_metrics = self.face_system.performance_monitor.get_latest_metrics()
                if latest_metrics:
                    live_data['system_status']['fps'] = latest_metrics.fps
                    live_data['system_status']['faces_detected'] = latest_metrics.faces_detected
            
            # إرسال للعملاء المتصلين
            self.socketio.emit('live_data', live_data)
            
        except Exception as e:
            self.logger.error(f"خطأ في إرسال البيانات المباشرة: {e}")
    
    def start_server(self, host='0.0.0.0', port=5000, debug=False):
        """بدء تشغيل الخادم"""
        try:
            self.is_running = True
            
            # بدء خيط إرسال البيانات المباشرة
            threading.Thread(target=self._live_data_sender, daemon=True).start()
            
            self.logger.info(f"بدء تشغيل خادم الويب على {host}:{port}")
            self.socketio.run(self.app, host=host, port=port, debug=debug)
            
        except Exception as e:
            self.logger.error(f"خطأ في تشغيل خادم الويب: {e}")
    
    def _live_data_sender(self):
        """خيط إرسال البيانات المباشرة"""
        while self.is_running:
            try:
                self._send_live_data()
                time.sleep(1)  # إرسال كل ثانية
            except Exception as e:
                self.logger.error(f"خطأ في خيط البيانات المباشرة: {e}")
                time.sleep(5)
    
    def stop_server(self):
        """إيقاف الخادم"""
        self.is_running = False
        self.logger.info("تم إيقاف خادم الويب")
