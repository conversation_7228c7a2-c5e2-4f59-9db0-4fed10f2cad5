// ملف خاص لحذف الأزرار المكررة من cache المتصفح
console.log('🧹 بدء تنظيف الأزرار المكررة...');

document.addEventListener('DOMContentLoaded', function() {
    // انتظار تحميل الصفحة بالكامل
    setTimeout(function() {
        cleanDuplicateButtons();
    }, 1000);
});

function cleanDuplicateButtons() {
    console.log('🔍 البحث عن الأزرار المكررة...');
    
    // 1. حذف جميع الأزرار المكررة لـ "لوحة التحكم الإدارية"
    const adminLinks = document.querySelectorAll('a[href="/admin"]');
    console.log(`عدد أزرار لوحة التحكم الإدارية الموجودة: ${adminLinks.length}`);
    
    if (adminLinks.length > 1) {
        console.log('⚠️ تم العثور على أزرار مكررة - سيتم حذفها');
        
        // الاحتفاظ بالزر الأول فقط وحذف الباقي
        for (let i = 1; i < adminLinks.length; i++) {
            const button = adminLinks[i];
            const parent = button.closest('.nav-section, .admin-control-section, .admin-btn-content');
            
            if (parent) {
                console.log(`🗑️ حذف الزر المكرر رقم ${i + 1}`);
                parent.remove();
            } else {
                console.log(`🗑️ حذف الزر المكرر رقم ${i + 1} (مباشر)`);
                button.remove();
            }
        }
        
        console.log('✅ تم حذف جميع الأزرار المكررة');
    } else {
        console.log('✅ لا توجد أزرار مكررة');
    }
    
    // 2. حذف أي أزرار "الصفحة الرئيسية" مكررة
    const homeLinks = document.querySelectorAll('a[href="/"]');
    console.log(`عدد أزرار الصفحة الرئيسية: ${homeLinks.length}`);
    
    if (homeLinks.length > 1) {
        console.log('⚠️ أزرار الصفحة الرئيسية مكررة - سيتم حذفها');
        for (let i = 1; i < homeLinks.length; i++) {
            homeLinks[i].remove();
        }
    }
    
    // 3. حذف أي عناصر فارغة أو غير مرغوب فيها
    const emptySections = document.querySelectorAll('.nav-section:empty, .admin-control-section:empty');
    emptySections.forEach(section => {
        console.log('🗑️ حذف قسم فارغ');
        section.remove();
    });
    
    // 4. إضافة تأثير بصري للزر الإداري المتبقي
    const remainingAdminLink = document.querySelector('a[href="/admin"]');
    if (remainingAdminLink) {
        console.log('✨ إضافة تأثير بصري للزر الإداري');
        remainingAdminLink.style.background = 'linear-gradient(135deg, rgba(220, 53, 69, 0.2), rgba(220, 53, 69, 0.1))';
        remainingAdminLink.style.borderLeft = '4px solid #dc3545';
        remainingAdminLink.style.fontWeight = 'bold';
        remainingAdminLink.style.boxShadow = '0 0 10px rgba(220, 53, 69, 0.3)';
        
        // إضافة تأثير نبضة
        setTimeout(() => {
            remainingAdminLink.style.boxShadow = '';
        }, 3000);
    }
    
    console.log('🎉 تم الانتهاء من تنظيف القائمة');
}

// تشغيل التنظيف عند تغيير الصفحة أيضاً
window.addEventListener('beforeunload', function() {
    console.log('🔄 تنظيف cache قبل مغادرة الصفحة');
});

// تشغيل التنظيف كل 5 ثوان للتأكد
setInterval(function() {
    const adminLinks = document.querySelectorAll('a[href="/admin"]');
    if (adminLinks.length > 1) {
        console.log('🔄 تم العثور على أزرار مكررة جديدة - سيتم تنظيفها');
        cleanDuplicateButtons();
    }
}, 5000);
