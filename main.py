import cv2
import numpy as np
import face_recognition
import os
import sys

path = 'persons'
images = []
classNames = []

# التحقق من وجود مجلد الصور
if not os.path.exists(path):
    print(f"خطأ: مجلد {path} غير موجود!")
    sys.exit(1)

personsList = os.listdir(path)
print(f"تم العثور على {len(personsList)} صورة في مجلد {path}")

for cl in personsList:
    if cl.lower().endswith(('.png', '.jpg', '.jpeg')):
        curPersonn = cv2.imread(f'{path}/{cl}')
        if curPersonn is not None:
            images.append(curPersonn)
            classNames.append(os.path.splitext(cl)[0])
        else:
            print(f"تحذير: لا يمكن قراءة الصورة {cl}")

print(f"الأشخاص المحملون: {classNames}")

def findEncodings(images):
    encodeList = []
    for i, img in enumerate(images):
        try:
            img = cv2.cvtColor(img, cv2.COLOR_BGR2RGB)
            encodings = face_recognition.face_encodings(img)
            if len(encodings) > 0:
                encodeList.append(encodings[0])
                print(f"تم ترميز الصورة {i+1}/{len(images)}")
            else:
                print(f"تحذير: لم يتم العثور على وجه في الصورة {i+1}")
        except Exception as e:
            print(f"خطأ في ترميز الصورة {i+1}: {e}")
    return encodeList

if len(images) == 0:
    print("خطأ: لا توجد صور صالحة للمعالجة!")
    sys.exit(1)

encodeListKnown = findEncodings(images)
print('اكتمل ترميز الوجوه.')

# محاولة فتح الكاميرا
cap = cv2.VideoCapture(0)
if not cap.isOpened():
    print("خطأ: لا يمكن فتح الكاميرا!")
    sys.exit(1)

print("تم فتح الكاميرا بنجاح. اضغط 'q' للخروج.")

while True:
    success, img = cap.read()
    if not success:
        print("خطأ: لا يمكن قراءة الإطار من الكاميرا")
        break

    # تصغير الصورة لتسريع المعالجة
    imgS = cv2.resize(img, (0,0), None, 0.25, 0.25)
    imgS = cv2.cvtColor(imgS, cv2.COLOR_BGR2RGB)

    # البحث عن الوجوه في الإطار الحالي
    faceCurentFrame = face_recognition.face_locations(imgS)
    encodeCurentFrame = face_recognition.face_encodings(imgS, faceCurentFrame)

    # مقارنة كل وجه تم العثور عليه مع الوجوه المعروفة
    for encodeface, faceLoc in zip(encodeCurentFrame, faceCurentFrame):
        matches = face_recognition.compare_faces(encodeListKnown, encodeface)
        faceDis = face_recognition.face_distance(encodeListKnown, encodeface)
        matchIndex = np.argmin(faceDis)

        # إذا كان هناك تطابق مع دقة جيدة
        if matches[matchIndex] and faceDis[matchIndex] < 0.6:
            name = classNames[matchIndex].upper()
            confidence = round((1 - faceDis[matchIndex]) * 100, 2)
            print(f"تم التعرف على: {name} - الدقة: {confidence}%")

            # رسم مربع حول الوجه
            y1, x2, y2, x1 = faceLoc
            y1, x2, y2, x1 = y1*4, x2*4, y2*4, x1*4
            cv2.rectangle(img, (x1, y1), (x2, y2), (0,255,0), 2)
            cv2.rectangle(img, (x1,y2-35), (x2,y2), (0,255,0), cv2.FILLED)
            cv2.putText(img, f"{name} ({confidence}%)", (x1+6, y2-6), cv2.FONT_HERSHEY_COMPLEX, 0.6, (255,255,255), 2)
        else:
            # وجه غير معروف
            y1, x2, y2, x1 = faceLoc
            y1, x2, y2, x1 = y1*4, x2*4, y2*4, x1*4
            cv2.rectangle(img, (x1, y1), (x2, y2), (0,0,255), 2)
            cv2.rectangle(img, (x1,y2-35), (x2,y2), (0,0,255), cv2.FILLED)
            cv2.putText(img, "غير معروف", (x1+6, y2-6), cv2.FONT_HERSHEY_COMPLEX, 0.6, (255,255,255), 2)

    cv2.imshow('نظام التعرف على الوجوه', img)
    if cv2.waitKey(1) & 0xFF == ord('q'):
        break

cap.release()
cv2.destroyAllWindows()