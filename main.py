#!/usr/bin/env python3
"""
نظام التعرف على الوجوه الاحترافي
Professional Face Recognition System

المطور: مشروع تعليمي متقدم
الإصدار: 2.0.0
التاريخ: 2025
"""

import sys
import os

# إضافة مسار المشروع إلى Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from src.core.face_recognition_system import FaceRecognitionSystem
from src.utils.logger import setup_logger
from src.config.settings import Settings

def main():
    """الدالة الرئيسية لتشغيل النظام"""
    try:
        # إعداد نظام التسجيل
        logger = setup_logger()
        logger.info("بدء تشغيل نظام التعرف على الوجوه الاحترافي")

        # تحميل الإعدادات
        settings = Settings()

        # إنشاء وتشغيل النظام
        system = FaceRecognitionSystem(settings)
        system.run()

    except KeyboardInterrupt:
        logger.info("تم إيقاف النظام بواسطة المستخدم")
    except Exception as e:
        logger.error(f"خطأ في تشغيل النظام: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()