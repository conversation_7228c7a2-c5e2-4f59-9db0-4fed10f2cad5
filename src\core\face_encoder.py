"""
نظام ترميز الوجوه المحسن
Enhanced Face Encoding System
"""

import cv2
import numpy as np
import face_recognition
from pathlib import Path
from typing import List, Tuple, Optional
import time

from ..config.settings import Settings
from ..utils.logger import get_logger


class FaceEncoder:
    """فئة ترميز الوجوه مع تحسينات الأداء"""
    
    def __init__(self, settings: Settings):
        self.settings = settings
        self.logger = get_logger()
    
    def encode_faces_from_images(self, image_paths: List[Path]) -> Tuple[List[np.ndarray], List[str]]:
        """ترميز الوجوه من قائمة الصور"""
        encodings = []
        names = []
        
        self.logger.info(f"بدء ترميز {len(image_paths)} صورة...")
        
        for i, image_path in enumerate(image_paths):
            try:
                # قراءة الصورة
                image = cv2.imread(str(image_path))
                if image is None:
                    self.logger.warning(f"لا يمكن قراءة الصورة: {image_path}")
                    continue
                
                # تحويل من BGR إلى RGB
                rgb_image = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
                
                # البحث عن الوجوه
                face_locations = face_recognition.face_locations(
                    rgb_image, 
                    model=self.settings.recognition.face_detection_model
                )
                
                if not face_locations:
                    self.logger.warning(f"لم يتم العثور على وجه في الصورة: {image_path.name}")
                    continue
                
                if len(face_locations) > 1:
                    self.logger.warning(f"تم العثور على أكثر من وجه في الصورة: {image_path.name}")
                
                # ترميز الوجه الأول
                face_encodings = face_recognition.face_encodings(
                    rgb_image, 
                    face_locations,
                    model=self.settings.recognition.face_encoding_model
                )
                
                if face_encodings:
                    encodings.append(face_encodings[0])
                    name = image_path.stem  # اسم الملف بدون الامتداد
                    names.append(name)
                    
                    self.logger.info(f"تم ترميز الصورة {i+1}/{len(image_paths)}: {name}")
                else:
                    self.logger.warning(f"فشل في ترميز الوجه في الصورة: {image_path.name}")
                
            except Exception as e:
                self.logger.error(f"خطأ في معالجة الصورة {image_path}: {e}")
                continue
        
        self.logger.info(f"تم ترميز {len(encodings)} وجه بنجاح من أصل {len(image_paths)} صورة")
        return encodings, names
    
    def encode_face_from_frame(self, frame: np.ndarray, face_location: Tuple) -> Optional[np.ndarray]:
        """ترميز وجه من إطار فيديو"""
        try:
            # تحويل من BGR إلى RGB
            rgb_frame = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
            
            # ترميز الوجه
            encodings = face_recognition.face_encodings(
                rgb_frame, 
                [face_location],
                model=self.settings.recognition.face_encoding_model
            )
            
            return encodings[0] if encodings else None
            
        except Exception as e:
            self.logger.error(f"خطأ في ترميز الوجه من الإطار: {e}")
            return None
    
    def detect_faces_in_frame(self, frame: np.ndarray) -> List[Tuple]:
        """اكتشاف الوجوه في إطار"""
        try:
            # تصغير الإطار لتسريع المعالجة
            small_frame = cv2.resize(
                frame, 
                (0, 0), 
                fx=self.settings.performance.image_resize_factor,
                fy=self.settings.performance.image_resize_factor
            )
            
            # تحويل من BGR إلى RGB
            rgb_small_frame = cv2.cvtColor(small_frame, cv2.COLOR_BGR2RGB)
            
            # اكتشاف الوجوه
            face_locations = face_recognition.face_locations(
                rgb_small_frame,
                model=self.settings.recognition.face_detection_model
            )
            
            # تكبير المواقع للحجم الأصلي
            scale_factor = 1 / self.settings.performance.image_resize_factor
            scaled_locations = []
            
            for (top, right, bottom, left) in face_locations:
                scaled_top = int(top * scale_factor)
                scaled_right = int(right * scale_factor)
                scaled_bottom = int(bottom * scale_factor)
                scaled_left = int(left * scale_factor)
                scaled_locations.append((scaled_top, scaled_right, scaled_bottom, scaled_left))
            
            return scaled_locations[:self.settings.recognition.max_faces_per_frame]
            
        except Exception as e:
            self.logger.error(f"خطأ في اكتشاف الوجوه: {e}")
            return []
    
    def compare_faces(self, known_encodings: List[np.ndarray], face_encoding: np.ndarray) -> Tuple[List[bool], List[float]]:
        """مقارنة وجه مع الوجوه المعروفة"""
        try:
            # مقارنة الوجوه
            matches = face_recognition.compare_faces(
                known_encodings, 
                face_encoding,
                tolerance=self.settings.recognition.recognition_tolerance
            )
            
            # حساب المسافات
            distances = face_recognition.face_distance(known_encodings, face_encoding)
            
            return matches, distances.tolist()
            
        except Exception as e:
            self.logger.error(f"خطأ في مقارنة الوجوه: {e}")
            return [], []
    
    def get_best_match(self, known_encodings: List[np.ndarray], known_names: List[str], 
                      face_encoding: np.ndarray) -> Tuple[Optional[str], float, bool]:
        """الحصول على أفضل تطابق"""
        try:
            if not known_encodings:
                return None, 0.0, False
            
            matches, distances = self.compare_faces(known_encodings, face_encoding)
            
            if not any(matches):
                return None, 0.0, False
            
            # العثور على أفضل تطابق
            best_match_index = np.argmin(distances)
            
            if matches[best_match_index]:
                name = known_names[best_match_index]
                distance = distances[best_match_index]
                confidence = (1 - distance) * 100
                
                # التحقق من عتبة الثقة
                is_confident = distance <= self.settings.recognition.confidence_threshold
                
                return name, confidence, is_confident
            
            return None, 0.0, False
            
        except Exception as e:
            self.logger.error(f"خطأ في العثور على أفضل تطابق: {e}")
            return None, 0.0, False
    
    def validate_image_quality(self, image: np.ndarray) -> Tuple[bool, str]:
        """التحقق من جودة الصورة"""
        try:
            # التحقق من الحجم
            height, width = image.shape[:2]
            if height < 100 or width < 100:
                return False, "الصورة صغيرة جداً"
            
            # التحقق من الوضوح (باستخدام Laplacian variance)
            gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
            laplacian_var = cv2.Laplacian(gray, cv2.CV_64F).var()
            
            if laplacian_var < 100:  # عتبة الوضوح
                return False, "الصورة غير واضحة"
            
            # التحقق من الإضاءة
            mean_brightness = np.mean(gray)
            if mean_brightness < 50:
                return False, "الصورة مظلمة جداً"
            elif mean_brightness > 200:
                return False, "الصورة مضيئة جداً"
            
            return True, "جودة الصورة جيدة"
            
        except Exception as e:
            return False, f"خطأ في فحص الجودة: {e}"
    
    def preprocess_image(self, image: np.ndarray) -> np.ndarray:
        """معالجة مسبقة للصورة لتحسين الجودة"""
        try:
            # تحسين التباين
            lab = cv2.cvtColor(image, cv2.COLOR_BGR2LAB)
            l, a, b = cv2.split(lab)
            
            # تطبيق CLAHE على قناة الإضاءة
            clahe = cv2.createCLAHE(clipLimit=2.0, tileGridSize=(8, 8))
            l = clahe.apply(l)
            
            # دمج القنوات
            enhanced = cv2.merge([l, a, b])
            enhanced = cv2.cvtColor(enhanced, cv2.COLOR_LAB2BGR)
            
            # تقليل الضوضاء
            denoised = cv2.bilateralFilter(enhanced, 9, 75, 75)
            
            return denoised
            
        except Exception as e:
            self.logger.error(f"خطأ في المعالجة المسبقة: {e}")
            return image
